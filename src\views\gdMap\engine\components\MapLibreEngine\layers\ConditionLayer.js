import * as turf from "@turf/turf";
import request from "@/utils/request";
import emitter from "@/utils/emitter";

const LINE_COLOR = {
	3: "#00FF2B", // 畅通
	2: "#FADD00", // 缓慢
	1: "#FF0000", // 拥堵
	0: "#440001",
};

const LINE_OFFSET = 4;
const LINE_TRANSLATE = {
	// 左侧
	0: [-LINE_OFFSET, -LINE_OFFSET],
	// 右侧
	1: [LINE_OFFSET, LINE_OFFSET],
};

/**
 * 路况图层
 */
export class ConditionLayer {
	constructor(map) {
		this.map = map;
		this.baseSourceId = "base-source";
		this.baseLayerId = "base-layer";
		this.conditionSourceId = "condition-source";
		this.conditionLayerId = "condition-layer";
		this.IdMap = new Map();

		this.setupEventListeners();
	}

	setupEventListeners() {
		emitter.$on("menu-active", this.handleMenuActive.bind(this));
	}

	/**
	 * 初始化
	 */
	async init() {
		try {
			await Promise.all([this.loadLine(), this.loadData()]);
			this.addBaseLayer();
			this.setUpWorker();
		} catch (error) {
			console.log(error);
		}
	}

	/**
	 * 加载道路数据
	 */
	async loadLine() {
		const response = await fetch(
			"/config/engine/map/json/road/route_driving_EPSG4490_1751362729859.json"
		);
		const geoJSON = await response.json();
		const coords = geoJSON.features[0].geometry.coordinates;
		const resultCoords = [coords[0]];
		// 遍历剩余点，只添加与前一个点不同的点
		for (let i = 1; i < coords.length; i++) {
			const prev = coords[i - 1];
			const current = coords[i];

			if (prev[0] !== current[0] || prev[1] !== current[1]) {
				resultCoords.push(current);
			}
		}
		this.roadLine = turf.lineString(resultCoords);
	}

	/**
	 * 加载拥堵数据
	 */
	async loadData() {
		const { data = [] } = await request.get("/api/screen/baotong/center/road/traffic");
		this.dataList = data.filter((item) => {
			const { startCoordinates, endCoordinates } = item;
			return startCoordinates?.length === 2 && endCoordinates?.length === 2;
		});
	}

	/**
	 * 添加路况底图
	 */
	addBaseLayer() {
		this.map.addSource(this.baseSourceId, {
			type: "geojson",
			data: this.roadLine,
		});

		// 遍历添加双向两条
		for (let i = 0; i < 2; i++) {
			const layerId = this.baseLayerId + "-" + i;
			this.map.addLayer({
				id: layerId,
				type: "line",
				source: this.baseSourceId,
				layout: {
					"line-join": "round",
					"line-cap": "round",
				},
				paint: {
					"line-color": LINE_COLOR[3], // 默认畅通
					"line-width": 4,
					// "line-translate": LINE_TRANSLATE[i],
					"line-offset": i ? LINE_OFFSET : -LINE_OFFSET,
				},
			});

			this.IdMap.set(layerId, { layerId });
		}
	}

	/**
	 * 初始化worker
	 */
	setUpWorker() {
		this.worker = new Worker(new URL("./worker/ConditionWorker.js", import.meta.url), {
			type: "module",
		});
		this.worker.onmessage = this.handleWorkerMessage.bind(this);

		this.worker.postMessage({
			line: this.roadLine,
			items: this.dataList,
		});
	}

	/**
	 * worker事件处理
	 * @param {*} e
	 */
	handleWorkerMessage(e) {
		const processedItems = e.data;
		this.renderConditions(processedItems);
	}

	/**
	 * 渲染拥堵路况
	 * @param {*} processedItems
	 */
	renderConditions(processedItems) {
		processedItems.forEach((item) => {
			const { geojson, roadState, direction, sign } = item;
			this.addConditionLayer({
				id: sign,
				geojson,
				roadState,
				direction: direction === 1 ? 0 : 1, //二值化 1(那曲)->0; 2(格尔木)->1
			});
		});
	}

	/**
	 * 添加路况图层
	 * @param {*} param0
	 */
	async addConditionLayer({ id, geojson, roadState, direction }) {
		const sourceId = `condition-source-${id}`;
		const layerId = `condition-layer-${id}`;

		this.map.addSource(sourceId, {
			type: "geojson",
			data: geojson,
		});

		this.map.addLayer({
			id: layerId,
			type: "line",
			source: sourceId,
			layout: {
				"line-join": "miter",
				"line-cap": "square",
			},
			paint: {
				"line-color": LINE_COLOR[roadState],
				"line-width": 4,
				// "line-translate": LINE_TRANSLATE[direction],
				"line-offset": direction ? LINE_OFFSET : -LINE_OFFSET,
			},
		});

		this.IdMap.set(layerId, { layerId });
	}

	handleMenuActive(data) {
		if (data === "traffic") {
			this.setLayerVisible(true);
		} else {
			this.setLayerVisible(false);
		}
	}

	setLayerVisible(visible) {
		const layers = Array.from(this.IdMap.values());

		layers.forEach(({ layerId }) => {
			this.map.setLayoutProperty(layerId, "visibility", visible ? "visible" : "none");
		});
	}

	destroy() {
		emitter.$off("menu-active", this.handleMenuActive);
	}
}
