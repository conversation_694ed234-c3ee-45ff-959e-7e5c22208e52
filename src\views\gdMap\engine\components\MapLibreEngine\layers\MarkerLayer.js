import { MarkerManager } from "./marker/MarkerManager";

export class MarkerLayer {
	constructor(map) {
		// 创建标记管理器
		this.markerManager = new MarkerManager(map);
		this.map = map;
		this.visible = true;
	}

	/**
	 * 从area-mark.json加载GeoJSON数据
	 */
	async init() {
		// 加载区域标记
		// await this.markerManager.loadAreaMarkers();
		// // 加载预警标记
		// await this.markerManager.loadAlertMarkers();
		// await this.markerManager.loadFacilityWarnMarkers();
		return this;
	}

	/**
	 * 显示所有标记
	 */
	show() {
		if (!this.visible) {
			this.markerManager.show();
			this.visible = true;
		}
		return this;
	}

	/**
	 * 隐藏所有标记
	 */
	hide() {
		if (this.visible) {
			this.markerManager.hide();
			this.visible = false;
		}
		return this;
	}

	/**
	 * 清除所有标记
	 */
	clear() {
		this.markerManager.clear();
		return this;
	}

	/**
	 * 销毁图层
	 */
	destroy() {
		this.markerManager.destroy();
	}
}
