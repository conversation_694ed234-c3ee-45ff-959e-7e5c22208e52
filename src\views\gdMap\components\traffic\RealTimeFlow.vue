<template>
  <TrafficCard title="实时交通流量">
    <!-- <RealTimeFlowChart :data="data" /> -->
    <div class="real-time-flow">
      <ScreenTable
        height="250px"
        :columns="tableColumns"
        headerBgColor="transparent"
        tableBgColor="transparent"
      >
        <div ref="listContainerRef" class="scroll-container">
          <div class="traffic-table">
            <div class="table-body">
              <div v-for="(item, index) in trafficData" :key="index" class="table-row">
                <div class="col road-section">{{ item.roadSection }}</div>
                <div class="col direction">{{ item.direction }}</div>
                <div class="col flow">{{ item.flow }}</div>
                <div class="col speed">{{ item.speed }}</div>
              </div>
            </div>
          </div>
        </div>
      </ScreenTable>
    </div>
  </TrafficCard>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount, watch } from "vue";
import request from "@/utils/request";
import RealTimeFlowChart from "./components/RealTimeFlowChart.vue";
import ScreenTable from "../ScreenTable.vue";
import emitter from "@/utils/emitter";
import { useMarquee } from "@/hooks/useMarquee";

const directionList = [1, 2];
const dataMap = ref(new Map());
const currentDepartmentId = ref(null);

// 表格列配置
const tableColumns = [
  { title: "检测路段", flex: 1 },
  { title: "方向", flex: 1, align: "center" },
  { title: "车流量(辆)", flex: 1, align: "center" },
  { title: "平均速度(km/h)", flex: 1, align: "center" },
];

// 使用跑马灯效果
const { containerRef: listContainerRef, resetScroll } = useMarquee({
  speed: 10, // 滚动速度
  delay: 3000, // 滚动到底部后停顿时间
  step: 1, // 每次滚动的像素
});

// 静态交通流量数据
const trafficData = ref([
  { roadSection: "K1+000-K1+500", direction: "上行", flow: 156, speed: 78 },
  { roadSection: "K1+500-K2+000", direction: "下行", flow: 142, speed: 82 },
  { roadSection: "K2+000-K2+500", direction: "上行", flow: 189, speed: 75 },
  { roadSection: "K2+500-K3+000", direction: "下行", flow: 167, speed: 80 },
  { roadSection: "K3+000-K3+500", direction: "上行", flow: 134, speed: 85 },
  { roadSection: "K3+500-K4+000", direction: "下行", flow: 198, speed: 72 },
  { roadSection: "K4+000-K4+500", direction: "上行", flow: 176, speed: 79 },
  { roadSection: "K4+500-K5+000", direction: "下行", flow: 153, speed: 83 },
  { roadSection: "K5+000-K5+500", direction: "上行", flow: 162, speed: 77 },
  { roadSection: "K5+500-K6+000", direction: "下行", flow: 145, speed: 81 },
  { roadSection: "K6+000-K6+500", direction: "上行", flow: 187, speed: 74 },
  { roadSection: "K6+500-K7+000", direction: "下行", flow: 169, speed: 78 },
  { roadSection: "K7+000-K7+500", direction: "上行", flow: 158, speed: 76 },
  { roadSection: "K7+500-K8+000", direction: "下行", flow: 173, speed: 84 },
  { roadSection: "K8+000-K8+500", direction: "上行", flow: 191, speed: 73 },
  { roadSection: "K8+500-K9+000", direction: "下行", flow: 164, speed: 79 },
  { roadSection: "K9+000-K9+500", direction: "上行", flow: 148, speed: 86 },
  { roadSection: "K9+500-K10+000", direction: "下行", flow: 182, speed: 71 },
  { roadSection: "K10+000-K10+500", direction: "上行", flow: 157, speed: 80 },
  { roadSection: "K10+500-K11+000", direction: "下行", flow: 166, speed: 77 },
]);

// 当数据变化时，重置滚动
watch(
  trafficData,
  () => {
    setTimeout(resetScroll, 300); // 延迟一点时间确保DOM已更新
  },
  { deep: true }
);

// 调用新的交通流量接口
const getTrafficFlowData = () => {
  request
    .get("/api/screen/baotong/traffic/flow/new")
    .then((res) => {
      console.log("交通流量接口数据:", res);
      console.log("交通流量详细数据:", res.data);

      // 将接口返回的数据转换为表格需要的格式
      if (res.data && Array.isArray(res.data)) {
        trafficData.value = res.data.map((item) => ({
          roadSection: item.station, // 检测路段
          direction: item.direction, // 方向
          flow: item.carNum, // 车流量
          speed: Math.round(parseFloat(item.averageSpeed)), // 平均速度取整
        }));
        console.log("处理后的交通流量数据:", trafficData.value);
      }
    })
    .catch((error) => {
      console.error("获取交通流量数据失败:", error);
      // 如果接口失败，保持使用静态数据
    });
};

onMounted(() => {
  // 监听区域双击事件
  emitter.$on("division-area-dblclick", handleAreaChange);
  getData();
  getTrafficFlowData(); // 调用新的交通流量接口

  // 确保滚动效果启动
  setTimeout(() => {
    resetScroll();
  }, 500);
});

onBeforeUnmount(() => {
  emitter.$off("division-area-dblclick", handleAreaChange);
});

const handleAreaChange = (eventData) => {
  console.log("实时交通流量 - 接收到区域双击事件:", eventData);
  currentDepartmentId.value = eventData.properties.id;
  getData();
};

const data = computed(() => {
  return Array.from(unref(dataMap)).map(([key, value]) => {
    return { label: key, ...value };
  });
});

const getData = () => {
  getSpeedData();
  getFlowData();
};

const getSpeedData = () => {
  const map = unref(dataMap);
  const baseParams = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  directionList.forEach((direction) => {
    const params = { ...baseParams, direction };
    request.get("/api/screen/baotong/traffic/flow/speed", params).then((res) => {
      if (res.code == 200) {
        (res.data || []).forEach(({ name = "", strNum = 0 }) => {
          const obj = map.get(name) || {};
          obj[`speed${direction}`] = strNum;
          map.set(name, obj);
        });
      }
    });
  });
};

const getFlowData = () => {
  const map = unref(dataMap);
  const baseParams = currentDepartmentId.value ? { departmentId: currentDepartmentId.value } : {};
  directionList.forEach((direction) => {
    const params = { ...baseParams, direction };
    request.get("/api/screen/baotong/traffic/flow/car", params).then((res) => {
      if (res.code == 200) {
        (res.data || []).forEach(({ name = "", num = 0 }) => {
          const obj = map.get(name) || {};
          obj[`car${direction}`] = num;
          map.set(name, obj);
        });
      }
    });
  });
};
</script>

<style lang="scss" scoped>
.real-time-flow {
  height: 250px;

  // 覆盖ScreenTable的默认表头样式
  :deep(.screen-table-header) {
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
      rgba(0, 138, 255, 0.1);
    box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
    border: 1px solid #008aff;
    border-image: linear-gradient(
        90deg,
        rgba(0, 138, 255, 0),
        rgba(0, 138, 255, 1),
        rgba(0, 138, 255, 0.2),
        rgba(0, 138, 255, 0)
      )
      1 1;
  }

  // 覆盖ScreenTable的默认背景
  :deep(.screen-table) {
    background-color: transparent;
    padding: 0;
  }
}

.scroll-container {
  height: 100%; // 容器需要有固定高度
  overflow: hidden; // 重要：设置为hidden，由hooks控制滚动
  position: relative; // 相对定位
}

.traffic-table {
  flex: 1;
  overflow-y: auto;

  .table-body {
    .table-row {
      display: flex;
      height: 28px;
      align-items: center;
      background: rgba(0, 138, 255, 0.05);
      border-bottom: 1px solid rgba(0, 138, 255, 0.08);

      &:last-child {
        border-bottom: none;
      }

      .col {
        padding: 0 9px;
        font-size: 12px;
        color: #fff;
        font-family: Source Han Sans CN, Source Han Sans CN;
        cursor: pointer;

        &.road-section {
          flex: 1;
          color: #fff;
          text-align: left;
        }

        &.direction {
          flex: 1;
          text-align: center;
          &:hover {
            color: #0783fa;
            font-weight: bold;
          }
        }

        &.flow {
          flex: 1;
          text-align: center;
        }

        &.speed {
          flex: 1;
          text-align: center;
        }
      }
    }
  }
}
</style>
