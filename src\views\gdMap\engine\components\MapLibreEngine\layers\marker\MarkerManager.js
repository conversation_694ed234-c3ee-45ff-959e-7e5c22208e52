import { MarkerFactory } from "./MarkerFactory";
import emitter from "@/utils/emitter";
import request from "@/utils/request";
import { useAppStore } from "@/store";
import mapConfig from "@/config/engine/maplibre/map.config.js";
/**
 * 标记管理器
 * 用于统一管理所有标记
 */
export class MarkerManager {
	/**
	 * 构造函数
	 * @param {Object} map - MapLibre地图实例
	 */
	constructor(map) {
		this.map = map;
		this.markers = {};
		this.iconMapping = {};
		this.iconsLoaded = false;

		// 设置事件监听
		this.setupEventListeners();

		// 初始化时加载图标
		// this.initIcons();

		this.initClusters();

		// // 初始化请求一次状态
		// emitter.$emit("request-menu-active");
		const AppStore = useAppStore();
		this.handleMenuActive(AppStore.getActiveMenu);
	}

	/**
	 * 初始化图标加载
	 */
	async initIcons() {
		try {
			// 加载图标并等待完成
			await this.loadIcons();
			this.iconsLoaded = true;

			// 触发图标加载完成事件
			emitter.$emit("icons-loaded");
		} catch (error) {
			console.error("初始化图标加载失败:", error);
		}
	}

	async initClusters() {
		if (!this.clusterData) {
			const cluster = await fetch("/config/engine/map/json/mark/cluster.json")
				.then((res) => res.json())
				.then((obj) => obj.features);
			this.clusterData = cluster;
		}
	}

	/**
	 * 设置事件监听
	 */
	setupEventListeners() {
		// 监听菜单切换
		emitter.$on("menu-active", this.handleMenuActive.bind(this));

		// 监听标记更新事件
		emitter.$on("update-markers", this.handleMarkerUpdate.bind(this));

		// 监听地图缩放事件
		this.map.on("zoom", () => {
			this.updateMarkersVisibility();
		});
	}

	/**
	 *	处理菜单切换
	 */
	handleMenuActive(data) {
		if (data === "oneMap") {
			this.loadAreaMarkers();
			emitter.$on("footer-tab-change", this.handleOneMapFooterTabChange.bind(this));
		} else {
			this.removeAreaMarkers();
			this.removeStaffMarker();
			this.removeEngineeringMarker();
			emitter.$off("footer-tab-change", this.handleOneMapFooterTabChange);
			emitter.$off("staff-select", this.handleStaffSelect);
		}

		if (data === "traffic") {
			this.loadTrafficMarkers();
		} else {
			this.removeTrafficMarkers();
		}
	}

	/**
	 * 一图统管指挥模式菜单切换
	 */
	handleOneMapFooterTabChange(data, display) {
		// this.resetMapView();

		if (!display) data = -1; //切到看板模式

		if (data === 0) {
			this.loadStaffMarker();
			emitter.$on("staff-select", this.handleStaffSelect.bind(this));
		} else {
			this.removeStaffMarker();
			emitter.$off("staff-select", this.handleStaffSelect);
		}

		if (data === 1) {
			this.loadEngineeringMarker();
		} else {
			this.removeEngineeringMarker();
		}

		if (data === 2) {
			this.loadMechanicalEquipmentMarker();
		} else {
			this.removeMechanicalEquipmentMarker();
		}

		if (data === 4) {
			this.loadTrafficMarkers();
		} else {
			this.removeTrafficMarkers();
		}
	}
	/**
	 * 人员分布定位
	 * @param {Array} data 坐标数组
	 */
	handleStaffSelect(data) {
		this.map.flyTo({
			center: data,
			zoom: 10,
			duration: 3000,
			bearing: 0,
			pitch: 0,
			essential: true,
		});
	}

	/**
	 * 处理标记更新事件
	 * @param {Object} data - 更新数据
	 */
	handleMarkerUpdate(data) {
		// const { categoryKey, isVisible, features, layerTypeMapping } = data;
		// if (categoryKey === "type") {
		// 	// 检查图层是否已存在
		// 	const sourceId = "facility-markers-source";
		// 	const layerId = `${sourceId}-layer`;
		// 	const clusterLayerId = `${sourceId}-cluster-layer`;
		// 	const sourceExists = this.map.getSource(sourceId);
		// 	if (isVisible) {
		// 		if (sourceExists) {
		// 			// 如果数据源已存在，只需要更新可见性
		// 			this.setLayerVisibility(layerId, true);
		// 			this.setLayerVisibility(clusterLayerId, true);
		// 		} else {
		// 			// 如果数据源不存在，创建新的标记
		// 			this.loadFacilityMarkers(features, layerTypeMapping);
		// 		}
		// 	} else {
		// 		if (sourceExists) {
		// 			// 隐藏图层而不是移除
		// 			this.setLayerVisibility(layerId, false);
		// 			this.setLayerVisibility(clusterLayerId, false);
		// 		}
		// 	}
		// }
		// 其他图例类型的处理可以在这里添加

		const { type, category, visible } = data;
		MarkerFactory.setMarkerVisibility(this.map, type, visible, { category });
	}

	/**
	 * 设置图层可见性
	 * @param {String} layerId - 图层ID
	 * @param {Boolean} visible - 是否可见
	 */
	setLayerVisibility(layerId, visible) {
		if (this.map.getLayer(layerId)) {
			this.map.setLayoutProperty(layerId, "visibility", visible ? "visible" : "none");
		}
	}

	/**
	 * 处理聚合图层可见性
	 * @param {Object} data - 可见性数据
	 */
	handleClusterVisibility(data) {
		const { isVisible, sourceId, clusterLayerId } = data;

		if (!this.map) {
			console.warn("地图实例未初始化，无法切换聚合图层可见性");
			return;
		}

		try {
			// 设置聚合图层可见性
			this.setLayerVisibility(clusterLayerId, isVisible);

			// 同时设置对应的单点图层可见性
			const singleLayerId = `${sourceId}-layer`;
			this.setLayerVisibility(singleLayerId, isVisible);

			console.log(`已${isVisible ? "显示" : "隐藏"}聚合图层: ${clusterLayerId}`);
		} catch (error) {
			console.error(`切换聚合图层可见性失败: ${error.message}`);
		}
	}

	/**
	 * 加载工程设施标记
	 * @param {Array} features - GeoJSON特征数组
	 * @param {Object} layerTypeMapping - 图层类型映射
	 */
	async loadFacilityMarkers(features, layerTypeMapping) {
		try {
			// 移除现有的工程设施标记
			this.removeFacilityMarkers();

			// 定义工程类型
			const facilityTypes = [{ type: "facility", sourceId: "facility-markers-source" }];

			// 为每种类型创建标记
			facilityTypes.forEach(({ type, sourceId }) => {
				MarkerFactory.createMarker(type, this.map, sourceId, {
					data: { type: "FeatureCollection", features },
					iconMapping: this.iconMapping,
				});

				// 记录创建的标记
				this.markers[type] = { sourceId };
			});
		} catch (error) {
			console.error("加载工程设施标记失败:", error);
		}
	}

	/**
	 * 移除工程设施标记
	 */
	removeFacilityMarkers() {
		// 定义工程设施类型
		const facilityTypes = ["facility"];

		// 移除每种类型的标记
		facilityTypes.forEach((type) => {
			if (this.markers[type]) {
				MarkerFactory.removeMarker(type, this.map, this.markers[type].sourceId);
				delete this.markers[type];
			}
		});
	}

	/**
	 * 加载区域标记
	 */
	async loadAreaMarkers() {
		try {
			// 获取区域标记数据
			const response = await fetch("/config/engine/map/json/area-mark.json");
			const geoJsonData = await response.json();

			if (
				!geoJsonData ||
				geoJsonData.type !== "FeatureCollection" ||
				!Array.isArray(geoJsonData.features)
			) {
				console.error("无效的GeoJSON数据格式");
				return;
			}

			// 创建区域标记
			MarkerFactory.createMarker("area", this.map, "area-markers-source", {
				data: geoJsonData,
			});

			// 记录创建的标记
			this.markers["area"] = { sourceId: "area-markers-source" };
		} catch (error) {
			console.error("加载区域标记失败:", error);
		}
	}

	/**
	 * 移除区域标记
	 */
	removeAreaMarkers() {
		if (this.markers["area"]) {
			MarkerFactory.removeMarker("area", this.map, this.markers["area"].sourceId);
			delete this.markers["area"];
		}
	}

	/**
	 * 加载人员预警标记
	 */
	async loadAlertMarkers() {
		try {
			// 获取预警标记数据
			const response = await fetch("/config/engine/map/json/mark/monitor.mark.json");
			const geoJsonData = await response.json();

			if (
				!geoJsonData ||
				geoJsonData.type !== "FeatureCollection" ||
				!Array.isArray(geoJsonData.features)
			) {
				console.error("无效的预警GeoJSON数据格式");
				return;
			}

			// 创建预警标记
			MarkerFactory.createMarker("personnelAlert", this.map, "alert-markers-source", {
				data: geoJsonData,
			});

			// 记录创建的标记
			this.markers["alert"] = { sourceId: "alert-markers-source" };
		} catch (error) {
			console.error("加载预警标记失败:", error);
		}
	}

	/**
	 * 加载设备预警标记
	 */
	async loadFacilityWarnMarkers() {
		try {
			// 获取设备预警标记数据
			const response = await fetch("/config/engine/map/json/mark/facility-warn.mark.json");
			const geoJsonData = await response.json();

			if (
				!geoJsonData ||
				geoJsonData.type !== "FeatureCollection" ||
				!Array.isArray(geoJsonData.features)
			) {
				console.error("无效的设备预警GeoJSON数据格式");
				return;
			}

			// 创建设备预警标记
			MarkerFactory.createMarker("facilityWarn", this.map, "facility-warn-markers-source", {
				data: geoJsonData,
			});

			// 记录创建的标记
			this.markers["facilityWarn"] = { sourceId: "facility-warn-markers-source" };
		} catch (error) {
			console.error("加载设备预警标记失败:", error);
		}
	}

	/**
	 * 移除设备预警标记
	 */
	removeFacilityWarnMarkers() {
		if (this.markers["facilityWarn"]) {
			MarkerFactory.removeMarker("facilityWarn", this.map, this.markers["facilityWarn"].sourceId);
			delete this.markers["facilityWarn"];
		}
	}

	/**
	 * 移除预警标记
	 */
	removeAlertMarkers() {
		if (this.markers["alert"]) {
			MarkerFactory.removeMarker("alert", this.map, this.markers["alert"].sourceId);
			delete this.markers["alert"];
		}
	}

	/**
	 * 加载保通标记
	 */
	async loadTrafficMarkers() {
		try {
			{
				// 获取交通事件标记数据
				const { data = [] } = await request.get("/api/screen/baotong/center/event/list");

				const geojson = {
					type: "FeatureCollection",
					features: data.map((item) => ({
						type: "Feature",
						geometry: {
							type: "Point",
							coordinates: item.coordinates,
						},
						properties: item,
					})),
				};
				// 创建交通事件标记
				MarkerFactory.createMarker("trafficAccident", this.map, "", {
					data: geojson,
				});
			}

			{
				// 获取保通设备标记数据
				const { data = [] } = await request.get("/api/screen/baotong/center/device/list");

				const geojson = {
					type: "FeatureCollection",
					features: data.map((item) => ({
						type: "Feature",
						geometry: {
							type: "Point",
							coordinates: item.coordinates,
						},
						properties: item,
					})),
				};

				// 创建保通设备标记
				MarkerFactory.createMarker("trafficDevice", this.map, "", {
					data: geojson,
				});
			}

			{
				const { data = [] } = await request.get("/api/screen/baotong/center/congestion/list");

				const geojson = {
					type: "FeatureCollection",
					features: data.map((item) => ({
						type: "Feature",
						geometry: {
							type: "Point",
							coordinates: item.coordinates,
						},
						properties: item,
					})),
				};

				MarkerFactory.createMarker("trafficAlarm", this.map, "", {
					data: geojson,
				});
			}
		} catch (error) {
			console.error("加载标记失败:", error);
		}
	}

	/**
	 * 移除保通标记
	 */
	removeTrafficMarkers() {
		MarkerFactory.removeMarker("trafficAccident", this.map, "");
		MarkerFactory.removeMarker("trafficDevice", this.map, "");
		MarkerFactory.removeMarker("trafficAlarm", this.map, "");
	}

	/**
	 * 加载人员分布标记
	 */
	async loadStaffMarker() {
		{
			const { data = [] } = await request.get("/api/screen/staff/center/department/staff/list");

			const geojson = {
				type: "FeatureCollection",
				features: data.map((item) => ({
					type: "Feature",
					geometry: {
						type: "Point",
						coordinates: this.getMatchCoordinates(item.departmentId, 0),
					},
					properties: item,
				})),
			};

			MarkerFactory.createMarker("staff", this.map, "", {
				type: "people",
				data: geojson,
			});
		}

		{
			const { data = [] } = await request.get("/api/screen/staff/center/department/fence/list");

			const geojson = {
				type: "FeatureCollection",
				features: data.map((item) => ({
					type: "Feature",
					geometry: {
						type: "Point",
						coordinates: this.getMatchCoordinates(item.departmentId, 1),
					},
					properties: item,
				})),
			};

			MarkerFactory.createMarker("staff", this.map, "", {
				type: "fence",
				data: geojson,
			});
		}

		{
			const { data = [] } = await request.get("/api/screen/staff/center/sos/list");

			const geojson = {
				type: "FeatureCollection",
				features: data.map((item) => ({
					type: "Feature",
					geometry: {
						type: "Point",
						coordinates: item.coordinates,
					},
					properties: item,
				})),
			};

			MarkerFactory.createMarker("staff", this.map, "", {
				type: "sos",
				data: geojson,
			});
		}
	}

	/**
	 * 移除人员分布标记
	 */
	removeStaffMarker() {
		MarkerFactory.removeMarker("staff", this.map, "");
	}

	async loadEngineeringMarker() {
		{
			const { data = [] } = await request.get(
				"/api/screen/engineering/center/department/culvert/list"
			);

			const geojson = {
				type: "FeatureCollection",
				features: data.map((item) => ({
					type: "Feature",
					geometry: {
						type: "Point",
						coordinates: this.getMatchCoordinates(item.departmentId, 0),
					},
					properties: item,
				})),
			};

			MarkerFactory.createMarker("engineering", this.map, "", {
				type: "culvert",
				data: geojson,
			});
		}

		{
			const { data = [] } = await request.get(
				"/api/screen/engineering/center/department/bridge/list"
			);

			const geojson = {
				type: "FeatureCollection",
				features: data.map((item) => ({
					type: "Feature",
					geometry: {
						type: "Point",
						coordinates: this.getMatchCoordinates(item.departmentId, 1),
					},
					properties: item,
				})),
			};

			MarkerFactory.createMarker("engineering", this.map, "", {
				type: "bridge",
				data: geojson,
			});
		}
	}

	removeEngineeringMarker() {
		MarkerFactory.removeMarker("engineering", this.map, "");
	}

	/**
	 * 加载机械设备标记
	 */
	async loadMechanicalEquipmentMarker() {
		// 存储车辆详情数据，用于缩放时切换显示
		this.vehicleDetailData = null;
		// 定义固定坐标点位，按照departmentId顺序对应
		const fixedCoordinates = [
			{ departmentId: "gg1", coordinates: [94.77767437340106, 36.165924441735925] },
			{ departmentId: "gg2", coordinates: [94.81954217314006, 36.11229175617511] },
			{ departmentId: "gg3", coordinates: [94.82242960760544, 35.9605230461779] },
			{ departmentId: "gg4", coordinates: [94.62608406400147, 35.90207262790463] },
			{ departmentId: "gn1", coordinates: [93.59810517915571, 35.411815628583696] },
			{ departmentId: "gn2", coordinates: [93.59810517915571, 35.411815628583696] }, // 注意：gn2使用相同坐标
		];

		let vehicleEquipmentData = [];

		// 尝试加载机械设备车辆数据
		try {
			const { data = [] } = await request.get("/api/screen/equipment/center/department/car/list");
			console.log("机械设备车辆数据:", data);

			// 将接口数据与固定坐标进行匹配
			vehicleEquipmentData = data.map((item) => {
				const coordData = fixedCoordinates.find(
					(coord) => coord.departmentId === item.departmentId
				);
				return {
					id: `vehicle-equipment-${item.departmentId}`,
					name: `车辆设备-${item.departmentId}`,
					coordinates: coordData ? coordData.coordinates : [0, 0],
					departmentId: item.departmentId,
					canNumber: item.canNumber,
					onlineNumber: item.onlineNumber,
					onlinePercent: (item.onlinePercent * 100).toFixed(2), // 转换为百分比
					equipmentType: "车辆设备",
					status: item.onlineNumber > 0 ? "在线" : "离线",
					location: this.getDepartmentName(item.departmentId),
				};
			});
		} catch (error) {
			console.error("加载机械设备车辆数据失败:", error);

			// 如果接口失败，创建默认的固定点位数据
			vehicleEquipmentData = fixedCoordinates.map((coord) => ({
				id: `vehicle-equipment-${coord.departmentId}`,
				name: `车辆设备-${coord.departmentId}`,
				coordinates: coord.coordinates,
				departmentId: coord.departmentId,
				canNumber: 0,
				onlineNumber: 0,
				onlinePercent: "0.00",
				equipmentType: "车辆设备",
				status: "离线",
				location: this.getDepartmentName(coord.departmentId),
			}));
		}

		// 尝试加载车辆列表数据
		try {
			const { data = [] } = await request.get("/api/screen/equipment/center/car/list");
			console.log("车辆列表数据:", data);

			// 存储车辆详情数据，用于缩放时显示
			this.vehicleDetailData = data
				.map((item) => {
					// 验证坐标数据
					if (
						!item.coordinates ||
						!Array.isArray(item.coordinates) ||
						item.coordinates.length === 0
					) {
						console.warn(`车辆 ${item.equipId} 坐标数据无效:`, item.coordinates);
						return null;
					}

					let lng, lat;

					// 处理车辆接口的坐标格式：["94.46614479466928 35.9948498585448"]
					if (item.coordinates.length === 1 && typeof item.coordinates[0] === "string") {
						const coordStr = item.coordinates[0].trim();
						const coordParts = coordStr.split(/\s+/); // 使用正则分割空格

						if (coordParts.length !== 2) {
							console.warn(`车辆 ${item.equipId} 坐标字符串格式错误:`, coordStr);
							return null;
						}

						lng = parseFloat(coordParts[0]);
						lat = parseFloat(coordParts[1]);
					} else if (item.coordinates.length >= 2) {
						// 兼容其他可能的格式：[94.46614479466928, 35.9948498585448]
						lng = parseFloat(item.coordinates[0]);
						lat = parseFloat(item.coordinates[1]);
					} else {
						console.warn(`车辆 ${item.equipId} 坐标格式不支持:`, item.coordinates);
						return null;
					}

					// 检查坐标是否为有效数字
					if (isNaN(lng) || isNaN(lat)) {
						console.warn(`车辆 ${item.equipId} 坐标转换失败: lng=${lng}, lat=${lat}`);
						return null;
					}

					// 检查坐标范围是否合理（经度-180到180，纬度-90到90）
					if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
						console.warn(`车辆 ${item.equipId} 坐标超出有效范围: lng=${lng}, lat=${lat}`);
						return null;
					}

					return {
						id: `vehicle-detail-${item.equipId}`,
						name: `车辆-${item.equipId}`,
						coordinates: [lng, lat],
						equipId: item.equipId,
						location: item.location,
						deviceType: item.deviceType,
						alarm: item.alarm,
					};
				})
				.filter((item) => item !== null); // 过滤掉无效的数据
		} catch (error) {
			console.error("加载车辆列表数据失败:", error);
			this.vehicleDetailData = []; // 设置为空数组
		}

		// 存储车辆设备数据，用于缩放时切换显示
		this.vehicleEquipmentData = vehicleEquipmentData;

		// 创建车辆设备点位标记
		const geojson = {
			type: "FeatureCollection",
			features: vehicleEquipmentData.map((item) => ({
				type: "Feature",
				geometry: {
					type: "Point",
					coordinates: item.coordinates,
				},
				properties: item,
			})),
		};

		MarkerFactory.createMarker("staff", this.map, "", {
			type: "vehicle-equipment",
			data: geojson,
		});

		// 添加缩放监听器，实现聚合效果
		this.setupMechanicalEquipmentZoomHandler();

		// 加载电子围栏点位（与人员分布时一致）
		await this.loadMechanicalFenceMarkers();
	}

	/**
	 * 加载机械设备模式下的电子围栏标记
	 */
	async loadMechanicalFenceMarkers() {
		try {
			const { data = [] } = await request.get("/api/screen/staff/center/department/fence/list");

			const geojson = {
				type: "FeatureCollection",
				features: data.map((item) => ({
					type: "Feature",
					geometry: {
						type: "Point",
						coordinates: this.getMatchCoordinates(item.departmentId, 1),
					},
					properties: item,
				})),
			};

			// 缓存电子围栏数据，用于后续重新创建
			this.mechanicalFenceData = geojson;

			// 使用与人员分布模式相同的sourceId，保持一致的显示逻辑
			MarkerFactory.createMarker("staff", this.map, "", {
				type: "fence",
				data: geojson,
			});
		} catch (error) {
			console.error("加载机械设备电子围栏失败:", error);
		}
	}

	/**
	 * 确保电子围栏标记存在
	 */
	ensureFenceMarkersExist() {
		if (this.mechanicalFenceData) {
			// 检查是否已存在电子围栏标记
			const existingFenceMarkers = this.map.StaffMarkerMap
				? Array.from(this.map.StaffMarkerMap.keys()).filter((id) => id.includes("-fence-"))
				: [];

			if (existingFenceMarkers.length === 0) {
				// 如果不存在电子围栏标记，重新创建
				MarkerFactory.createMarker("staff", this.map, "", {
					type: "fence",
					data: this.mechanicalFenceData,
				});
			}
		}
	}

	/**
	 * 只移除车辆相关的标记，保留电子围栏标记
	 */
	removeVehicleMarkersOnly() {
		if (this.map.StaffMarkerMap) {
			const markersToRemove = [];

			this.map.StaffMarkerMap.forEach((marker, id) => {
				// 只移除车辆相关的标记（vehicle-equipment, vehicle-detail），保留电子围栏标记（fence）
				if (id.includes("-vehicle-equipment-") || id.includes("-vehicle-detail-")) {
					markersToRemove.push(id);
				}
			});

			// 移除车辆标记
			markersToRemove.forEach((id) => {
				const marker = this.map.StaffMarkerMap.get(id);
				if (marker) {
					marker.remove();
					this.map.StaffMarkerMap.delete(id);
				}
			});
		}
	}

	/**
	 * 移除机械设备标记
	 */
	removeMechanicalEquipmentMarker() {
		// 移除所有机械设备相关标记（包括车辆和电子围栏）
		MarkerFactory.removeMarker("staff", this.map, "");

		// 清理定时器
		if (this.mechanicalZoomTimeout) {
			clearTimeout(this.mechanicalZoomTimeout);
			this.mechanicalZoomTimeout = null;
		}

		// 移除缩放监听器
		if (this.map._mechanicalEquipmentZoomHandler) {
			this.map.off("zoom", this.map._mechanicalEquipmentZoomHandler);
			delete this.map._mechanicalEquipmentZoomHandler;
		}

		// 清理数据和状态
		this.vehicleDetailData = null;
		this.vehicleEquipmentData = null;
		this.mechanicalFenceData = null;
		this.currentMechanicalMarkerType = null;
	}

	getMatchCoordinates(departmentId, index = 0) {
		const feature = this.clusterData.find(({ properties }) => {
			return properties.departmentId === departmentId && properties.index == index;
		});

		if (feature) {
			const {
				geometry: { coordinates },
			} = feature;
			return coordinates;
		}

		return [0, 0];
	}

	/**
	 * 获取部门名称
	 * @param {String} departmentId - 部门ID
	 * @returns {String} 部门名称
	 */
	getDepartmentName(departmentId) {
		const departmentMap = {
			gg1: "格贡一分部",
			gg2: "格贡二分部",
			gg3: "格贡三分部",
			gg4: "格贡四分部",
			gn1: "贡那一分部",
			gn2: "贡那二分部",
		};
		return departmentMap[departmentId] || departmentId;
	}

	/**
	 * 设置机械设备缩放处理器
	 */
	setupMechanicalEquipmentZoomHandler() {
		// 移除已有的缩放监听器
		if (this.map._mechanicalEquipmentZoomHandler) {
			this.map.off("zoom", this.map._mechanicalEquipmentZoomHandler);
		}

		// 初始化状态
		this.currentMechanicalMarkerType = null;
		this.mechanicalZoomTimeout = null;

		// 创建防抖的缩放监听器
		this.map._mechanicalEquipmentZoomHandler = () => {
			// 清除之前的定时器
			if (this.mechanicalZoomTimeout) {
				clearTimeout(this.mechanicalZoomTimeout);
			}

			// 使用防抖机制，避免频繁触发
			this.mechanicalZoomTimeout = setTimeout(() => {
				const currentZoom = this.map.getZoom();
				const zoomThreshold = 8; // 缩放阈值

				const targetType = currentZoom >= zoomThreshold ? "detail" : "equipment";

				// 只有当目标类型与当前类型不同时才切换
				if (this.currentMechanicalMarkerType !== targetType) {
					this.currentMechanicalMarkerType = targetType;

					if (targetType === "detail") {
						// 放大到一定程度时，显示详细车辆点位
						this.showVehicleDetailMarkers();
					} else {
						// 缩小时，显示固定点位
						this.showVehicleEquipmentMarkers();
					}
				}
			}, 100); // 100ms 防抖延迟
		};

		// 添加缩放监听器
		this.map.on("zoom", this.map._mechanicalEquipmentZoomHandler);

		// 初始化显示状态
		this.map._mechanicalEquipmentZoomHandler();
	}

	/**
	 * 显示车辆详情标记（高缩放级别）
	 */
	showVehicleDetailMarkers() {
		if (!this.vehicleDetailData || this.vehicleDetailData.length === 0) {
			console.warn("车辆详情数据为空，无法显示详情标记");
			return;
		}

		try {
			// 只移除车辆相关的标记，不影响电子围栏
			this.removeVehicleMarkersOnly();

			// 过滤并验证坐标数据
			const validFeatures = this.vehicleDetailData
				.filter((item) => {
					// 再次验证坐标数据
					if (
						!item.coordinates ||
						!Array.isArray(item.coordinates) ||
						item.coordinates.length !== 2
					) {
						console.warn(`过滤无效坐标数据: ${item.id}`, item.coordinates);
						return false;
					}

					const [lng, lat] = item.coordinates;
					if (isNaN(lng) || isNaN(lat)) {
						console.warn(`过滤NaN坐标数据: ${item.id}`, item.coordinates);
						return false;
					}

					return true;
				})
				.map((item) => ({
					type: "Feature",
					geometry: {
						type: "Point",
						coordinates: item.coordinates,
					},
					properties: item,
				}));

			if (validFeatures.length === 0) {
				console.warn("没有有效的车辆详情坐标数据");
				return;
			}

			// 创建车辆详情标记
			const geojson = {
				type: "FeatureCollection",
				features: validFeatures,
			};

			console.log(`准备显示 ${validFeatures.length} 个车辆详情标记`);

			MarkerFactory.createMarker("staff", this.map, "", {
				type: "vehicle-detail",
				data: geojson,
			});
		} catch (error) {
			console.error("显示车辆详情标记时出错:", error);
		}
	}

	/**
	 * 显示车辆设备标记（低缩放级别）
	 */
	showVehicleEquipmentMarkers() {
		try {
			// 只移除车辆相关的标记，不影响电子围栏
			this.removeVehicleMarkersOnly();

			// 重新创建固定的车辆设备标记
			if (this.vehicleEquipmentData && this.vehicleEquipmentData.length > 0) {
				const geojson = {
					type: "FeatureCollection",
					features: this.vehicleEquipmentData.map((item) => ({
						type: "Feature",
						geometry: {
							type: "Point",
							coordinates: item.coordinates,
						},
						properties: item,
					})),
				};

				MarkerFactory.createMarker("staff", this.map, "", {
					type: "vehicle-equipment",
					data: geojson,
				});
			} else {
				console.warn("车辆设备数据为空，无法显示设备标记");
			}
		} catch (error) {
			console.error("显示车辆设备标记时出错:", error);
		}
	}

	/**
	 * 加载图标映射
	 */
	async loadIconMapping() {
		try {
			// 如果已经加载过图标映射，直接返回
			if (Object.keys(this.iconMapping).length > 0) {
				return this.iconMapping;
			}

			// 获取图例数据
			const response = await fetch("/mock/yizhangtu/legendData.json");
			const legendData = await response.json();

			// 创建category到icon URL的映射
			const iconMapping = {};

			// 查找工程类型分类
			const typeCategory = legendData.categories.find((c) => c.key === "type");
			if (typeCategory && typeCategory.items) {
				// 为每个工程类型创建映射
				typeCategory.items.forEach((item) => {
					if (item.category && item.icon) {
						iconMapping[item.category] = item.icon;
					}
				});
			}

			this.iconMapping = iconMapping;
			return iconMapping;
		} catch (error) {
			console.error("加载图标映射失败:", error);
			return {};
		}
	}

	/**
	 * 加载图标
	 */
	async loadIcons() {
		try {
			// 加载图标映射
			const iconMapping = await this.loadIconMapping();

			// 创建图标加载任务
			const loadTasks = [];

			// 加载图例中定义的图标
			for (const [category, iconUrl] of Object.entries(iconMapping)) {
				// 从URL中提取文件名
				const iconName = `${category}`;

				loadTasks.push(
					new Promise((resolve) => {
						const img = new Image();
						img.src = iconUrl;
						img.onload = () => {
							// console.log('iconName', iconName);
							this.map.addImage(iconName, img);
							resolve();
						};
						img.onerror = (error) => {
							console.error(`Failed to load icon: ${iconName}`, error);
							resolve(); // 即使某个图标加载失败，也继续加载其他图标
						};
					})
				);
			}

			// 加载其他固定图标
			const fixedIcons = [
				{ name: "command-center-icon", fileName: "general-command-marker.svg" },
				{ name: "gegong1", fileName: "ge-gong-yi-w0zj.svg" },
				{ name: "gegong2", fileName: "ge-gong-er-t1wr.svg" },
				{ name: "gegong3", fileName: "ge-gong-san-r2yx.svg" },
				{ name: "gegong4", fileName: "ge-gong-si-gwqa.svg" },
				{ name: "gongna1", fileName: "gong-na-yi-dknk.svg" },
				{ name: "gongna2", fileName: "gong-na-er-hgys.svg" },
				// 预警图标
				{ name: "alert-icon", fileName: "alert-icon.svg" },
				{ name: "facility-warn-icon", fileName: "facility-warn-icon.svg" },
				// 添加更多图标...
				{ name: "area-pedestal", fileName: "area-pedestal.svg" },
				{ name: "area-pedestal-red", fileName: "area-pedestal-red.svg" },
				{ name: "da-qiao", fileName: "da-qiao.svg" },
				{ name: "ban-he-zhan", fileName: "ban-he-zhan.svg" },
				{ name: "pile-point", fileName: "pile-point.svg" },
				{ name: "culvert-icon", fileName: "culvert-icon.svg" },
			];

			fixedIcons.forEach((icon) => {
				loadTasks.push(
					new Promise((resolve) => {
						const img = new Image();
						img.src = `/assets/images/icons/map/${icon.fileName}`;
						img.onload = () => {
							this.map.addImage(icon.name, img);
							resolve();
						};
						img.onerror = (error) => {
							console.error(`Failed to load icon: ${icon.name}`, error);
							resolve();
						};
					})
				);
			});

			// 等待所有图标加载完成
			await Promise.all(loadTasks);
		} catch (error) {
			console.error("加载图标失败:", error);
		}
	}

	/**
	 * 更新标记可见性
	 */
	updateMarkersVisibility() {
		// const currentZoom = this.map.getZoom();
		// 遍历所有标记，根据缩放级别更新可见性
		// Object.entries(this.markers).forEach(([type, { sourceId }]) => {
		//   // 获取标记的缩放级别范围
		//   let minZoom = 0;
		//   let maxZoom = 22;
		//   console.log('type', type);
		//   // 根据标记类型设置不同的缩放级别范围
		//   if (type === 'area') {
		//     minZoom = 5;
		//     maxZoom = 22;
		//   } else if (['bridge', 'culvert'].includes(type)) {
		//     minZoom = 7;
		//     maxZoom = 22;
		//   } else {
		//     minZoom = 6;
		//     maxZoom = 22;
		//   }
		// 根据缩放级别设置可见性
		//   const visible = currentZoom >= minZoom && currentZoom <= maxZoom;
		//   MarkerFactory.setMarkerVisibility(type, this.map, sourceId, visible);
		// });
	}

	/**
	 * 显示所有标记
	 */
	show() {
		Object.entries(this.markers).forEach(([type, { sourceId }]) => {
			MarkerFactory.setMarkerVisibility(type, this.map, sourceId, true);
		});
	}

	/**
	 * 隐藏所有标记
	 */
	hide() {
		Object.entries(this.markers).forEach(([type, { sourceId }]) => {
			MarkerFactory.setMarkerVisibility(type, this.map, sourceId, false);
		});
	}

	/**
	 * 清除所有标记
	 */
	clear() {
		// 移除所有标记
		Object.entries(this.markers).forEach(([type, { sourceId }]) => {
			MarkerFactory.removeMarker(type, this.map, sourceId);
		});

		// 清空标记记录
		this.markers = {};
	}

	/**
	 * 销毁管理器
	 */
	destroy() {
		// 清除所有标记
		this.clear();

		// 移除事件监听
		emitter.$off("menu-active", this.handleMenuActive);
		emitter.$off("footer-tab-change", this.handleOneMapFooterTabChange);
		emitter.$off("update-markers", this.handleMarkerUpdate);
		// emitter.$off("toggle-cluster-visibility");
		this.map.off("zoom");
	}

	/**
	 * 恢复地图视角
	 */
	resetMapView() {
		this.map.flyTo({
			center: mapConfig.center,
			zoom: mapConfig.minzoom,
			duration: 3000,
			bearing: 0,
			pitch: 0,
			essential: true,
		});
	}
}
