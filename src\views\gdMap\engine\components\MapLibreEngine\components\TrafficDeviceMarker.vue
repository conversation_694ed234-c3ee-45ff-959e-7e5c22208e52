<template>
	<DefaultMarker
		panelTitle="设备详情"
		:markerClass="markerClass"
		:coordinates="props.properties['coordinates']"
	>
		<template #icon>
			<component :is="icon"></component>
		</template>
		<template #content>
			<div class="data-list">
				<div class="data-item">
					<span class="data-key">设备类型:</span>
					<span class="data-value">
						{{ TrafficDeviceType.getLabel(props.properties["deviceType"]) }}
					</span>
				</div>
				<div class="data-item">
					<span class="data-key">设备名称:</span>
					<span class="data-value"> {{ props.properties["deviceName"] }} </span>
				</div>
				<div class="data-item">
					<span class="data-key">设备状态:</span>
					<span class="data-value">
						{{ props.properties["onlineState"] ? "在线" : "离线" }}
					</span>
				</div>
				<div class="data-item">
					<span class="data-key">位置:</span>
					<span class="data-value"> {{ props.properties["location"] }} </span>
				</div>
				<div class="data-item">
					<span class="data-key">方向:</span>
					<span class="data-value"> {{ props.properties["deviceName"] }} </span>
				</div>
				<div class="data-item">
					<span class="data-key">工区:</span>
					<span class="data-value"> {{ props.properties["deviceName"] }} </span>
				</div>
			</div>
		</template>
	</DefaultMarker>
</template>

<script setup>
import { TrafficDeviceType } from "@/utils/dict";
import light from "@/assets/modules/traffic/icon/footer-device-light.svg?component";
import camera from "@/assets/modules/traffic/icon/footer-device-camera.svg?component";
import adjustment from "@/assets/modules/traffic/icon/footer-device-adjustment.svg?component";
import edge from "@/assets/modules/traffic/icon/footer-device-edge.svg?component";
import DefaultMarker from "./DefaultMarker.vue";

const props = defineProps({
	properties: {
		type: Object,
		required: true,
	},
});

const markerClass = "is-blue";

const icon = computed(() => {
	const item = TrafficDeviceType.getItem(props.properties["deviceType"]);
	if (item) {
		return { light, camera, adjustment, edge }[item.icon];
	}
	return null;
});
</script>
