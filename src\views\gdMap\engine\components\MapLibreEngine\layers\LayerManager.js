import { MarkerLayer } from "./MarkerLayer";
import { PileLayer } from "./PileLayer";
import { RoadLayer } from "./RoadLayer";
import { TestSectionLayer } from "./line/TestSectionLayer";
import { MaskLayer } from "./MaskLayer";
import { BoundaryLayer } from "./BoundaryLayer";
import { RouteLayer } from "./RouteLayer";
import { ConditionLayer } from "./ConditionLayer";
import { CityLayer } from "./CityLayer";
import { DivisionLayer } from "./DivisionLayer";
import { EchartsLayer } from "./EchartsLayer";

/**
 * 统一的图层管理器
 * 负责管理所有地图图层的初始化、显示/隐藏和销毁
 */
export class LayerManager {
	/**
	 * 构造函数
	 * @param {Object} map MapLibre地图实例
	 */
	constructor(map) {
		this.map = map;
		this.layers = {}; // 存储所有图层实例
		this.layerTypes = {
			marker: MarkerLayer,
			// pile: PileLayer,
			road: RoadLayer,
			// testSection: TestSectionLayer, // 添加试验段图层
			mask: MaskLayer,
			boundary: BoundaryLayer,
			route: RouteLayer,
			condition: ConditionLayer,
			city: CityLayer,
			division: DivisionLayer, // 分部区域图层
			// echarts: EchartsLayer,
		};

		// 图层初始化状态
		this.initialized = false;
	}

	/**
	 * 初始化所有图层
	 * @param {Array} layerTypes 要初始化的图层类型数组，如果为空则初始化所有图层
	 * @returns {Promise<this>} 当前实例
	 */
	async initLayers(layerTypes = []) {
		try {
			// 如果没有指定图层类型，则初始化所有图层
			const typesToInit = layerTypes.length > 0 ? layerTypes : Object.keys(this.layerTypes);

			// 并行初始化所有图层
			const initPromises = typesToInit.map(async (type) => {
				if (!this.layerTypes[type]) {
					console.warn(`未知的图层类型: ${type}`);
					return;
				}

				// 创建图层实例
				const LayerClass = this.layerTypes[type];
				const layer = new LayerClass(this.map);

				// 初始化图层
				await layer.init?.();

				// 存储图层实例
				this.layers[type] = layer;

				// console.log(`图层 ${type} 初始化完成`);
			});

			// 等待所有图层初始化完成
			await Promise.all(initPromises);

			this.initialized = true;
			// console.log('所有图层初始化完成');

			this.sortZOrder();

			return this;
		} catch (error) {
			console.error("图层初始化失败:", error);
			throw error;
		}
	}

	/**
	 *	图层排序
	 */
	sortZOrder() {
		const orders = [];
		Object.values(this.layers).forEach((layer) => {
			const order = layer.ZOrder;
			if (typeof order === "number") {
				const { IdMap } = layer;
				orders.push({ order, IdMap });
			}
		});

		orders
			.sort((a, b) => a.order - b.order)
			.forEach(({ IdMap }) => {
				IdMap.forEach(({ layerId }) => {
					if (this.map.getLayer(layerId)) {
						this.map.moveLayer(layerId);
					}
				});
			});
	}

	/**
	 * 获取指定类型的图层实例
	 * @param {string} type 图层类型
	 * @returns {Object|null} 图层实例或null
	 */
	getLayer(type) {
		return this.layers[type] || null;
	}

	/**
	 * 显示指定图层
	 * @param {string} type 图层类型
	 * @returns {this} 当前实例
	 */
	showLayer(type) {
		const layer = this.getLayer(type);
		if (layer && typeof layer.show === "function") {
			layer.show();
		} else if (layer && typeof layer.setVisible === "function") {
			layer.setVisible(true);
		}
		return this;
	}

	/**
	 * 隐藏指定图层
	 * @param {string} type 图层类型
	 * @returns {this} 当前实例
	 */
	hideLayer(type) {
		const layer = this.getLayer(type);
		if (layer && typeof layer.hide === "function") {
			layer.hide();
		} else if (layer && typeof layer.setVisible === "function") {
			layer.setVisible(false);
		}
		return this;
	}

	/**
	 * 切换指定图层的可见性
	 * @param {string} type 图层类型
	 * @returns {this} 当前实例
	 */
	toggleLayer(type) {
		const layer = this.getLayer(type);
		if (layer && typeof layer.toggle === "function") {
			layer.toggle();
		} else if (layer) {
			// 如果没有toggle方法，但有show/hide方法
			if (layer.visible) {
				this.hideLayer(type);
			} else {
				this.showLayer(type);
			}
		}
		return this;
	}

	/**
	 * 设置指定图层的可见性
	 * @param {string} type 图层类型
	 * @param {boolean} visible 是否可见
	 * @returns {this} 当前实例
	 */
	setLayerVisible(type, visible) {
		if (visible) {
			this.showLayer(type);
		} else {
			this.hideLayer(type);
		}
		return this;
	}

	/**
	 * 注册新的图层类型
	 * @param {string} type 图层类型名称
	 * @param {Class} LayerClass 图层类
	 * @returns {this} 当前实例
	 */
	registerLayerType(type, LayerClass) {
		if (this.layerTypes[type]) {
			console.warn(`图层类型 ${type} 已存在，将被覆盖`);
		}
		this.layerTypes[type] = LayerClass;
		return this;
	}

	/**
	 * 添加自定义图层实例
	 * @param {string} type 图层类型名称
	 * @param {Object} layerInstance 图层实例
	 * @returns {this} 当前实例
	 */
	addCustomLayer(type, layerInstance) {
		if (this.layers[type]) {
			console.warn(`图层 ${type} 已存在，将被覆盖`);
		}
		this.layers[type] = layerInstance;
		return this;
	}

	/**
	 * 销毁所有图层
	 */
	destroy() {
		// 销毁所有图层实例
		Object.values(this.layers).forEach((layer) => {
			if (layer && typeof layer.destroy === "function") {
				layer.destroy();
			}
		});

		// 清空图层实例
		this.layers = {};
		this.initialized = false;
	}
}
