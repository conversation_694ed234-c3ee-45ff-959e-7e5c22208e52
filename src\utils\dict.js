export class Dict {
	constructor(data) {
		this.data = data;
	}

	getItem(value) {
		if (value) {
			return this.data.find((item) => item.value === value);
		}
		return null;
	}

	getLabel(value) {
		const item = this.getItem(value);
		if (item) {
			return item.label;
		}
		return "";
	}
}

export const TrafficAccidentType = new Dict([
	{
		value: 1,
		label: "交通拥堵",
		icon: "jam",
	},
	{
		value: 4,
		label: "道路封路",
		icon: "roadClosure",
	},
	{
		value: 2,
		label: "交通事故",
		icon: "accident",
	},
	{
		value: 3,
		label: "交通管制",
		icon: "otherAccident",
	},
]);

export const TrafficDeviceType = new Dict([
	{
		value: 12,
		label: "红绿灯",
		icon: "light",
	},
	{
		value: 7,
		label: "摄像机",
		icon: "camera",
	},
	{
		value: 13,
		label: "交调设备",
		icon: "adjustment",
	},
	{
		value: 14,
		label: "边缘计算",
		icon: "edge",
	},
]);

export const TrafficAlarmType = new Dict([
	{
		value: 1,
		label: "高疑似拥堵",
		class: "is-cyan",
		icon: "alarmBellPrimary",
	},
	{
		value: 2,
		label: "异常拥堵",
		class: "is-red",
		icon: "alarmBellDanger",
	},
	{
		value: 3,
		label: "常规拥堵",
		class: "is-yellow",
		icon: "alarmBellWarning",
	},
]);

export const departmentDict = new Dict([
	{
		value: "gg1",
		label: "格贡一",
	},
	{
		value: "gg2",
		label: "格贡二",
	},
	{
		value: "gg3",
		label: "格贡三",
	},
	{
		value: "gg4",
		label: "格贡四",
	},
	{
		value: "gn1",
		label: "贡那一",
	},
	{
		value: "gn2",
		label: "贡那二",
	},
]);
