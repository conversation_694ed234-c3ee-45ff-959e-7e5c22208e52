/**
 * MapLibre 地图配置文件
 */
// geoserver代理地址，对应vite.config.js中的代理地址
const geoserverProxy = import.meta.env.VITE_GEOSERVER_URL || "/geoserver-proxy";

export default {
	// 地图中心点
	center: [93.55076011473545, 34.05578533652968], // 当前为项目指挥部
	// 底图服务地址
	baseLayerUrl: `${geoserverProxy}/geoserver/gwc/service/wmts`,
	// 图层名称
	layerName: "G109NoFix_0327",
	tileMatrixSet: "EPSG:3857_G109NoFix_0327",
	bounds: [
		[69.23465791406244, 24.401406263439824],
		[111.54622622396664, 41.30118815698856],
	],

	// 最小层级
	minzoom: 6,
	// 最大层级
	maxzoom: 18,
	tiandituToken: "7eb2601ad8b7be9b959e476d44e5b3a8",
};
