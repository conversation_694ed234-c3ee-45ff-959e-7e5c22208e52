import mapConfig from "@/config/engine/maplibre/map.config.js";

// 图层缩放级别常量
const LAYER_ZOOM_LEVELS = {
	BASE: {
		MIN: mapConfig.minzoom,
		MAX: mapConfig.maxzoom,
	},
	GLOW: {
		MIN: mapConfig.minzoom,
		MAX: mapConfig.maxzoom,
	},
};

export class RoadLayer {
	constructor(map) {
		this.map = map;
		this.visible = true;
		this.animationFrame = null; // 用于存储动画帧ID
		this.sourceId = "roads"; // 数据源ID
		this.baseLayerId = "roads-base"; // 基础图层ID
		this.glowLayerId = "roads-glow"; // 流光图层ID
		this.ZOrder = 1;
		this.IdMap = new Map();
	}

	async init() {
		try {
			const response = await fetch(
				"/config/engine/map/json/road/route_driving_EPSG4490_1751362729859.json"
			);
			const geoJSON = await response.json();

			// 添加道路数据源，启用lineMetrics以支持line-progress
			this.map.addSource(this.sourceId, {
				type: "geojson",
				data: geoJSON,
				tolerance: 0.1,
				lineMetrics: true, // 启用线性度量，支持line-progress
			});

			// 添加基础道路图层
			this.map.addLayer({
				id: this.baseLayerId,
				type: "line",
				source: this.sourceId,
				layout: {
					"line-join": "round",
					"line-cap": "square",
					visibility: "visible",
				},
				paint: {
					"line-color": "#BDFFE7",
					"line-width": 6,
					// "line-opacity": 0.6, // 降低基础层不透明度
				},
			});

			this.IdMap.set("base", { sourceId: this.sourceId, layerId: this.baseLayerId });

			// [5, 4, 3, 2, 1].forEach((item) => {
			// 	const id = this.glowLayerId + "-" + item;
			// 	this.map.addLayer({
			// 		id: id,
			// 		type: "line",
			// 		source: this.sourceId,
			// 		layout: {
			// 			"line-join": "round",
			// 			"line-cap": "round",
			// 			visibility: "visible",
			// 		},
			// 		paint: {
			// 			"line-color": "hsl(35,100%,76%)", // 发光颜色
			// 			"line-width": 4,
			// 			"line-opacity": 1 / item, // 低不透明度
			// 			// "line-blur": item * 2,
			// 		},
			// 	});

			// 	this.map.on("zoomend", () => {
			// 		const zoom = this.map.getZoom();

			// 		let width;
			// 		if (zoom < 12) {
			// 			width = item * 2;
			// 		} else if (zoom > 12 && zoom < 15) {
			// 			width = item * 4;
			// 		} else {
			// 			width = item * 6;
			// 		}

			// 		this.map.setPaintProperty(id, "line-width", width);
			// 	});

			// 	this.IdMap.set("glow", { sourceId: this.sourceId, layerId: id });
			// });

			// this.map.addLayer({
			// 	id: this.glowLayerId,
			// 	type: "line",
			// 	source: this.sourceId,
			// 	layout: {
			// 		"line-join": "round",
			// 		"line-cap": "round",
			// 		visibility: "visible",
			// 	},
			// 	paint: {
			// 		"line-color": "#FFF", // 发光颜色
			// 		"line-width": 2,
			// 		"line-opacity": 1,
			// 	},
			// 	minzoom: 12,
			// 	maxzoom: LAYER_ZOOM_LEVELS.GLOW.MAX,
			// });

			// this.IdMap.set("glow", { sourceId: this.sourceId, layerId: this.glowLayerId });

			// this.map.addLayer({
			// 	id: this.glowLayerId,
			// 	type: "line",
			// 	source: this.sourceId,
			// 	layout: {
			// 		"line-join": "round",
			// 		"line-cap": "round",
			// 		visibility: "visible",
			// 	},
			// 	paint: {
			// 		"line-color": "#FFF", // 发光颜色
			// 		"line-width": [
			// 			"interpolate",
			// 			["linear"],
			// 			["zoom"], // 基于 zoom 层级
			// 			5,
			// 			6,
			// 			9,
			// 			14,
			// 			13,
			// 			22,
			// 		],
			// 		"line-opacity": 0.6, // 低不透明度
			// 		"line-blur": 12, // 较大的模糊值，创建发光效果
			// 	},
			// 	minzoom: LAYER_ZOOM_LEVELS.GLOW.MIN,
			// 	maxzoom: LAYER_ZOOM_LEVELS.GLOW.MAX,
			// });

			// this.IdMap.set("glow", { sourceId: this.sourceId, layerId: this.glowLayerId });

			// // 添加流光效果图层
			// this.map.addLayer({
			// 	id: this.glowLayerId,
			// 	type: "line",
			// 	source: this.sourceId,
			// 	layout: {
			// 		"line-join": "round",
			// 		"line-cap": "round",
			// 		visibility: "visible",
			// 	},
			// 	paint: {
			// 		// "line-color": "transparent",
			// 		"line-color": "hsl(35,100%,76%)",
			// 		// 初始渐变设置，将在动画中动态更新
			// 		"line-gradient": [
			// 			"interpolate",
			// 			["linear"],
			// 			["line-progress"],
			// 			0,
			// 			"rgba(253,255,221,0)",
			// 			0.5,
			// 			"rgba(253,255,221,0)",
			// 			1,
			// 			"rgba(253,255,221,0)",
			// 		],
			// 		"line-width": 8,
			// 		"line-opacity": 0.9, // 增加不透明度，使效果更明显
			// 	},
			// 	minzoom: LAYER_ZOOM_LEVELS.GLOW.MIN,
			// 	maxzoom: LAYER_ZOOM_LEVELS.GLOW.MAX,
			// });

			// // 启动流光动画
			// this.startFlowLightAnimation();

			return geoJSON;
		} catch (error) {
			console.error("加载道路数据失败:", error);
			throw error;
		}
	}

	// 流光动画方法 - 使用时间表达式
	startFlowLightAnimation() {
		// 如果已经有动画在运行，先停止它
		if (this.animationFrame) {
			cancelAnimationFrame(this.animationFrame);
			this.animationFrame = null;
		}

		let startTime = performance.now();
		const duration = 10000; // 增加周期时间，使动画更加平滑

		// 创建多个流光点，确保连贯性
		const flowPoints = 1; // 同时存在的流光点数量

		const animate = () => {
			const currentTime = performance.now();
			const elapsed = currentTime - startTime;

			// 更新流光图层的渐变位置
			if (this.map && this.map.getLayer(this.glowLayerId)) {
				try {
					// 使用简化的方法创建渐变
					const t = (elapsed / duration) % 1; // 当前动画进度 (0-1)

					// 创建基础渐变
					const gradientStops = [
						"interpolate",
						["linear"],
						["line-progress"],
						0,
						"rgba(253,255,221,0)", // 起始点始终透明
					];

					// 为每个流光点添加固定间隔的渐变点
					for (let i = 0; i < flowPoints; i++) {
						// 计算当前流光点的位置 (0-1范围内)
						const pointPos = (t + i / flowPoints) % 1;

						// 计算流光前后的位置，确保在0-1范围内
						const beforePos = Math.max(0, pointPos - 0.1);
						const midPos = pointPos;
						const afterPos = Math.min(1, pointPos + 0.1);

						// 确保位置值严格递增且不重复
						if (beforePos > 0 && beforePos < midPos) {
							gradientStops.push(beforePos, "rgba(253,255,221,0)");
						}

						gradientStops.push(midPos, "#FDFFDD");

						if (afterPos > midPos && afterPos < 1) {
							gradientStops.push(afterPos, "rgba(253,255,221,0)");
						}
					}

					// 确保结束点存在且值为1
					if (gradientStops[gradientStops.length - 2] < 1) {
						gradientStops.push(1, "rgba(253,255,221,0)");
					}

					// 应用渐变
					this.map.setPaintProperty(this.glowLayerId, "line-gradient", gradientStops);

					// 继续下一帧动画
					this.animationFrame = requestAnimationFrame(animate);
				} catch (error) {
					console.warn("流光动画更新失败:", error);
					// 出错时尝试重新启动动画
					setTimeout(() => this.startFlowLightAnimation(), 1000);
				}
			} else if (this.visible) {
				// 如果图层不存在但应该可见，尝试重新创建
				console.warn("流光图层不存在，尝试重新初始化");
				setTimeout(() => this.startFlowLightAnimation(), 1000);
			}
		};

		// 开始动画循环
		this.animationFrame = requestAnimationFrame(animate);
	}

	show() {
		if (!this.visible) {
			if (this.map.getLayer(this.baseLayerId)) {
				this.map.setLayoutProperty(this.baseLayerId, "visibility", "visible");
			}

			if (this.map.getLayer(this.glowLayerId)) {
				this.map.setLayoutProperty(this.glowLayerId, "visibility", "visible");
			} else {
				console.warn("流光图层不存在，无法显示");
			}

			// 重新启动流光动画
			this.startFlowLightAnimation();

			this.visible = true;
		}
	}

	hide() {
		if (this.visible) {
			if (this.map.getLayer(this.baseLayerId)) {
				this.map.setLayoutProperty(this.baseLayerId, "visibility", "none");
			}

			if (this.map.getLayer(this.glowLayerId)) {
				this.map.setLayoutProperty(this.glowLayerId, "visibility", "none");
			}

			// 停止流光动画以节省资源
			if (this.animationFrame) {
				cancelAnimationFrame(this.animationFrame);
				this.animationFrame = null;
			}

			this.visible = false;
		}
	}

	toggle() {
		if (this.visible) {
			this.hide();
		} else {
			this.show();
		}
	}

	// 销毁方法，用于清理资源
	destroy() {
		// 停止动画
		if (this.animationFrame) {
			cancelAnimationFrame(this.animationFrame);
			this.animationFrame = null;
		}

		// 移除图层
		if (this.map.getLayer(this.glowLayerId)) {
			this.map.removeLayer(this.glowLayerId);
		}

		if (this.map.getLayer(this.baseLayerId)) {
			this.map.removeLayer(this.baseLayerId);
		}

		// 移除数据源
		if (this.map.getSource(this.sourceId)) {
			this.map.removeSource(this.sourceId);
		}
	}
}
