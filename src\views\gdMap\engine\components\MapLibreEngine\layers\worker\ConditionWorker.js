import * as turf from "@turf/turf";

/**
 * 计算匹配路线
 * @param {*} e
 */
self.onmessage = function (e) {
	const { line, items } = e.data;

	const results = items
		.map((item) => {
			try {
				const { startCoordinates, endCoordinates, ...rest } = item;
				const startPoint = turf.point(startCoordinates.map(Number), rest);
				const endPoint = turf.point(endCoordinates.map(Number));

				// 在线上找到最近的点
				const startSnap = turf.nearestPointOnLine(line, startPoint);
				const endSnap = turf.nearestPointOnLine(line, endPoint);

				// 使用lineSlice提取线段
				const geojson = turf.lineSlice(startSnap, endSnap, line);

				return {
					...rest,
					geojson,
					startCoordinates,
					endCoordinates,
				};
			} catch (error) {
				console.error("Error processing item:", item, error);
				return null;
			}
		})
		.filter(Boolean);

	self.postMessage(results);
};
