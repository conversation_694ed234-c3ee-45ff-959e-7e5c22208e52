# 交通事件弹窗问题排查指南

## 🔍 快速排查步骤

### 1. 检查浏览器控制台
打开浏览器开发者工具 (F12)，查看Console标签：

```
预期看到的日志：
✅ "发送请求参数: {page: 0, size: 10}"
✅ "交通事件接口返回数据: {code: 200, data: {...}}"
✅ "表格数据设置完成: [...]"
✅ "总记录数: 4861"
✅ "总页数: 487"
✅ "paginatedData: [...]"
```

### 2. 检查网络请求
切换到Network标签，查看接口请求：

```
请求URL: /api/screen/baotong/traffic/event/page?page=0&size=10
请求方法: GET
响应状态: 200 OK
响应数据: 应该包含records数组
```

### 3. 检查Vue组件状态
在Console中输入以下命令检查组件状态：

```javascript
// 检查表格数据
console.log("tableData:", window.Vue?.devtools?.inspectInstance?.tableData);

// 检查分页数据
console.log("paginatedData:", window.Vue?.devtools?.inspectInstance?.paginatedData);
```

## 🐛 常见问题和解决方案

### 问题1: 表格完全为空
**可能原因:**
- 接口请求失败
- 数据格式不匹配
- Vue响应式数据未更新

**解决步骤:**
1. 检查控制台是否有错误信息
2. 验证接口返回的数据格式
3. 确认tableData.value是否有数据

### 问题2: 接口请求失败
**可能原因:**
- 网络连接问题
- 接口地址错误
- 权限验证失败

**解决步骤:**
1. 检查Network标签中的请求状态
2. 验证接口URL是否正确
3. 检查是否需要认证头

### 问题3: 数据有值但表格不显示
**可能原因:**
- 表格列配置错误
- Vue响应式更新问题
- CSS样式问题

**解决步骤:**
1. 检查columns配置是否正确
2. 验证slotName和dataIndex匹配
3. 检查表格容器是否有高度

### 问题4: 筛选功能不工作
**可能原因:**
- 参数名称不匹配
- 参数值格式错误
- 接口不支持筛选参数

**解决步骤:**
1. 检查发送的参数名称和值
2. 对照接口文档验证参数格式
3. 测试不同的筛选条件

## 🔧 临时调试方案

### 启用测试数据
如果接口有问题，可以临时启用测试数据：

1. 打开 `src/components/detailModal/TrafficEventModal.vue`
2. 找到 `open()` 方法中的注释代码
3. 取消注释以启用测试数据：

```javascript
// 取消这些行的注释
setTimeout(() => {
  if (tableData.value.length === 0) {
    console.log("使用测试数据");
    tableData.value = [
      {
        id: 1,
        eventType: 1,
        state: 0,
        departmentId: "格贡一",
        eventTime: "2025-08-04 09:00:59",
        updateTime: null,
        locationZhuang: "K2807+415"
      }
    ];
    totalRecords.value = 1;
    totalPages.value = 1;
  }
}, 2000);
```

### 添加更多调试信息
在getData方法中添加更多日志：

```javascript
console.log("当前页:", currentPage.value);
console.log("页面大小:", pageSize.value);
console.log("筛选条件:", {
  eventType: selectedEventType.value,
  eventState: selectedEventState.value,
  department: selectedDepartment.value
});
```

## 📋 完整测试清单

### 基础功能测试
- [ ] 弹窗能正常打开
- [ ] 弹窗标题显示正确
- [ ] 表格结构显示正常
- [ ] 加载状态正常显示

### 数据加载测试
- [ ] 接口请求成功发送
- [ ] 接口返回数据正确
- [ ] 表格数据正确显示
- [ ] 分页信息正确显示

### 交互功能测试
- [ ] 筛选条件正常工作
- [ ] 搜索按钮功能正常
- [ ] 重置按钮功能正常
- [ ] 分页翻页功能正常

### 数据格式测试
- [ ] 序号正确显示
- [ ] 部门名称正确显示
- [ ] 事件类型正确转换
- [ ] 事件状态正确转换
- [ ] 日期时间格式正确
- [ ] 空值处理正确

## 🚀 快速修复建议

如果问题仍然存在，请按以下顺序尝试：

1. **重启开发服务器**
   ```bash
   npm run dev
   ```

2. **清除浏览器缓存**
   - 按 Ctrl+Shift+R 强制刷新
   - 或清除浏览器缓存

3. **检查Vue版本兼容性**
   - 确认项目使用的Vue版本
   - 验证Composition API语法

4. **简化组件测试**
   - 先测试基本的弹窗打开功能
   - 再逐步添加数据加载功能

## 📞 获取帮助

如果以上步骤都无法解决问题，请提供：

1. 浏览器控制台的完整错误信息
2. Network标签中的接口请求详情
3. 当前使用的Vue和Arco Design版本
4. 具体的错误现象描述

这样可以更快速地定位和解决问题。
