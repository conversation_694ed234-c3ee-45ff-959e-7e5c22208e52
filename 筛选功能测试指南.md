# 交通事件弹窗筛选功能测试指南

## 🔧 已修复的问题

### 1. 下拉框宽度不一致
**修复内容:**
- 设置固定宽度：`width: 140px`
- 确保内部元素也是固定宽度：`width: 100% !important`
- 统一最小宽度：`min-width: 140px`

**测试方法:**
1. 打开弹窗
2. 观察三个下拉框的初始宽度
3. 分别选择不同选项
4. 确认宽度始终保持一致

### 2. 搜索条件未传入接口
**修复内容:**
- 增强参数验证逻辑
- 确保数字类型转换：`Number(selectedEventType.value)`
- 添加详细的调试日志
- 完善空值检查

**测试方法:**
1. 打开浏览器开发者工具 (F12)
2. 切换到Console标签
3. 打开弹窗并设置筛选条件
4. 点击搜索按钮
5. 查看控制台日志

## 🔍 测试步骤

### 步骤1: 基础功能测试
```
1. 点击"查看详情"打开弹窗
2. 观察下拉框宽度是否一致
3. 查看控制台是否有初始数据加载日志
```

### 步骤2: 筛选条件测试
```
1. 选择事件类型：拥堵
2. 选择事件状态：未处理  
3. 选择所属分部：格贡一分部
4. 点击搜索按钮
5. 查看控制台日志
```

### 步骤3: 验证参数传递
在控制台中应该看到类似的日志：
```
执行搜索，当前筛选条件: {eventType: 1, eventState: 0, department: "gg1"}
筛选条件原始值: {eventType: 1, eventState: 0, department: "gg1"}
发送请求参数: {page: 0, size: 10, eventType: 1, eventState: 0, departmentId: "gg1"}
```

### 步骤4: 重置功能测试
```
1. 设置一些筛选条件
2. 点击重置按钮
3. 确认所有下拉框都清空
4. 确认数据重新加载
```

## 📊 预期结果

### 控制台日志示例
```
✅ 正确的日志输出：
执行搜索，当前筛选条件: {eventType: 1, eventState: 0, department: "gg1"}
筛选条件原始值: {eventType: 1, eventState: 0, department: "gg1"}  
发送请求参数: {page: 0, size: 10, eventType: 1, eventState: 0, departmentId: "gg1"}
交通事件接口返回数据: {code: 200, data: {...}}
```

### 网络请求示例
```
✅ 正确的请求URL：
/api/screen/baotong/traffic/event/page?page=0&size=10&eventType=1&eventState=0&departmentId=gg1
```

### 下拉框宽度
```
✅ 所有下拉框宽度一致：140px
- 未选择时：140px
- 选择选项后：140px  
- 不同选项文字长度：140px
```

## 🐛 故障排查

### 如果筛选条件仍未传入：
1. 检查控制台是否有"筛选条件原始值"日志
2. 确认选项值是否正确绑定
3. 验证v-model绑定是否正常工作

### 如果下拉框宽度仍不一致：
1. 检查CSS是否正确应用
2. 清除浏览器缓存
3. 检查是否有其他CSS规则覆盖

### 调试命令
在浏览器控制台中执行：
```javascript
// 检查当前筛选条件值
console.log("当前筛选条件:", {
  eventType: document.querySelector('[placeholder="请选择"]')?.value,
  // 更多调试信息...
});
```

## 🎯 测试清单

- [ ] 下拉框宽度始终保持140px
- [ ] 事件类型筛选正常工作
- [ ] 事件状态筛选正常工作  
- [ ] 所属分部筛选正常工作
- [ ] 搜索按钮触发正确的接口请求
- [ ] 重置按钮清空所有筛选条件
- [ ] 控制台显示正确的调试日志
- [ ] 网络请求包含正确的查询参数
- [ ] 筛选后的数据正确显示在表格中

## 📞 如果问题仍然存在

请提供以下信息：
1. 浏览器控制台的完整日志
2. Network标签中的请求详情
3. 具体的问题现象截图
4. 使用的浏览器版本

这样可以更快速地定位和解决剩余问题。
