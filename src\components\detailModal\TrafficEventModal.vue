<template>
  <Modal ref="modalRef" title="交通事件检测记录" :icon="modelIconTitle">
    <div class="detail-modal-content">
      <!-- 筛选区域 -->
      <div class="filter-section">
        <div class="filter-row">
          <div class="filter-item">
            <div class="label">事件类型:</div>
            <a-select v-model:value="selectedEventType" placeholder="请选择" allow-clear>
              <a-option value="">全部</a-option>
              <a-option :value="1">拥堵</a-option>
              <a-option :value="2">交通事故</a-option>
              <a-option :value="3">交通管制</a-option>
              <a-option :value="4">封路</a-option>
            </a-select>
          </div>
          <div class="filter-item">
            <div class="label">事件状态:</div>
            <a-select v-model:value="selectedEventState" placeholder="请选择" allow-clear>
              <a-option value="">全部</a-option>
              <a-option :value="0">未处理</a-option>
              <a-option :value="1">已处理</a-option>
            </a-select>
          </div>
          <div class="filter-item">
            <div class="label">所属分部:</div>
            <a-select v-model:value="selectedDepartment" placeholder="请选择" allow-clear>
              <a-option value="">全部</a-option>
              <a-option value="gg1">格贡一分部</a-option>
              <a-option value="gg2">格贡二分部</a-option>
              <a-option value="gg3">格贡三分部</a-option>
              <a-option value="gg4">格贡四分部</a-option>
              <a-option value="gn1">贡那一分部</a-option>
              <a-option value="gn2">贡那二分部</a-option>
            </a-select>
          </div>
          <a-button class="search" @click="handleSearch">搜索</a-button>
          <a-button class="reset" @click="handleReset">重置</a-button>
        </div>
      </div>

      <!-- 表格区域 -->
      <div class="table-container">
        <div class="table-section">
          <a-table
            :columns="columns"
            :data="paginatedData"
            :pagination="false"
            :scroll="{ x: 1200 }"
            :loading="loading"
            row-key="id"
            size="small"
          >
            <template #index="{ rowIndex }">
              {{ (currentPage - 1) * pageSize + rowIndex + 1 }}
            </template>
            <template #departmentName="{ record }">
              {{ getDepartmentDisplayName(record.departmentId) }}
            </template>
            <template #eventType="{ record }">
              {{ getEventTypeLabel(record.eventType) }}
            </template>
            <template #eventState="{ record }">
              {{ getEventStateLabel(record.state) }}
            </template>
            <template #eventTime="{ record }">
              {{ formatDateTime(record.eventTime) }}
            </template>
            <template #updateTime="{ record }">
              {{ formatDateTime(record.updateTime) }}
            </template>
          </a-table>
        </div>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <div class="pagination-info">
            共<span class="pagination-number">{{ totalPages }}</span
            >页/<span class="pagination-number">{{ totalRecords }}</span
            >条数据
          </div>
          <div class="pagination">
            <div class="pagination-info">
              共<span class="pagination-number">{{ totalPages }}</span
              >页
            </div>
            <button
              class="pagination-btn prev"
              :disabled="currentPage === 1"
              @click="handlePageChange(currentPage - 1)"
            >
              <IconLeft />
            </button>
            <button
              v-for="page in visiblePages"
              :key="page"
              class="pagination-btn"
              :class="{
                active: page === currentPage,
                ellipsis: page === '...',
              }"
              :disabled="page === '...'"
              @click="page !== '...' && handlePageChange(page)"
            >
              {{ page }}
            </button>
            <button
              class="pagination-btn next"
              :disabled="currentPage === totalPages"
              @click="handlePageChange(currentPage + 1)"
            >
              <IconRight />
            </button>
          </div>
        </div>
      </div>
    </div>
  </Modal>
</template>

<script setup>
import { ref, computed } from "vue";
import modelIconTitle from "@/assets/modules/traffic/icon/model-icon-title.svg?component";
import Modal from "@/views/gdMap/components/traffic/components/Modal.vue";
import { IconLeft, IconRight } from "@arco-design/web-vue/es/icon";
import request from "@/utils/request";
import dayjs from "dayjs";

const modalRef = ref(null);
const loading = ref(false);

// 筛选条件
const selectedEventType = ref("");
const selectedEventState = ref("");
const selectedDepartment = ref("");

// 分页数据
const currentPage = ref(1);
const pageSize = ref(10);
const totalRecords = ref(0);
const totalPages = ref(0);

// 表格数据
const tableData = ref([]);

// 表格列配置
const columns = [
  {
    title: "序号",
    slotName: "index",
    width: 60,
    align: "center",
  },
  {
    title: "所属分部",
    slotName: "departmentName",
    width: 120,
  },
  {
    title: "事件类型",
    slotName: "eventType",
    width: 100,
    align: "center",
  },
  {
    title: "具体位置",
    dataIndex: "locationZhuang",
    width: 200,
  },
  {
    title: "事件状态",
    slotName: "eventState",
    width: 100,
    align: "center",
  },
  {
    title: "发生时间",
    slotName: "eventTime",
    width: 160,
    align: "center",
  },
  {
    title: "处理时间",
    slotName: "updateTime",
    width: 160,
    align: "center",
  },
];

// 分页后的数据
const paginatedData = computed(() => {
  return tableData.value;
});

// 可见页码计算
const visiblePages = computed(() => {
  const total = totalPages.value;
  const current = currentPage.value;
  const pages = [];

  if (total <= 7) {
    // 如果总页数小于等于7，显示所有页码
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // 总页数大于7时的逻辑
    if (current <= 4) {
      // 当前页在前4页
      for (let i = 1; i <= 5; i++) {
        pages.push(i);
      }
      pages.push("...");
      pages.push(total);
    } else if (current >= total - 3) {
      // 当前页在后4页
      pages.push(1);
      pages.push("...");
      for (let i = total - 4; i <= total; i++) {
        pages.push(i);
      }
    } else {
      // 当前页在中间
      pages.push(1);
      pages.push("...");
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i);
      }
      pages.push("...");
      pages.push(total);
    }
  }

  return pages;
});

/**
 * 获取部门名称（用于显示完整的分部名称）
 * @param {String} departmentName - 部门名称
 * @returns {String} 完整的部门名称
 */
const getDepartmentDisplayName = (departmentName) => {
  // 如果已经包含"分部"，直接返回，否则添加"分部"
  if (departmentName && !departmentName.includes("分部")) {
    return departmentName + "分部";
  }
  return departmentName || "-";
};

/**
 * 获取事件类型标签
 * @param {Number} eventType - 事件类型
 * @returns {String} 事件类型标签
 */
const getEventTypeLabel = (eventType) => {
  const typeMap = {
    1: "拥堵",
    2: "交通事故",
    3: "交通管制",
    4: "封路",
  };
  return typeMap[eventType] || "-";
};

/**
 * 获取事件状态标签
 * @param {Number} state - 事件状态
 * @returns {String} 事件状态标签
 */
const getEventStateLabel = (state) => {
  const stateMap = {
    0: "未处理",
    1: "已处理",
  };
  return stateMap[state] || "-";
};

/**
 * 格式化日期时间
 * @param {String} dateTime - 日期时间字符串
 * @returns {String} 格式化后的日期时间
 */
const formatDateTime = (dateTime) => {
  if (!dateTime) return "-";
  return dayjs(dateTime).format("YYYY-MM-DD HH:mm:ss");
};

// 获取数据
const getData = async () => {
  try {
    loading.value = true;

    const params = {
      page: currentPage.value - 1, // 后端页码从0开始
      size: pageSize.value,
    };

    // 添加筛选条件
    if (selectedEventType.value !== "") {
      params.eventType = selectedEventType.value;
    }
    if (selectedEventState.value !== "") {
      params.eventState = selectedEventState.value;
    }
    if (selectedDepartment.value !== "") {
      params.departmentId = selectedDepartment.value;
    }

    console.log("发送请求参数:", params);

    const res = await request.get("/api/screen/baotong/traffic/event/page", params);

    console.log("交通事件接口返回数据:", res);

    if (res && res.code === 200 && res.data && res.data.records) {
      // 直接使用返回的数据，departmentId已经是中文名称
      tableData.value = res.data.records;

      totalRecords.value = res.data.total || 0;
      totalPages.value = res.data.totalPages || 0;

      console.log("表格数据设置完成:", tableData.value);
      console.log("总记录数:", totalRecords.value);
      console.log("总页数:", totalPages.value);
      console.log("paginatedData:", paginatedData.value);
    } else {
      console.error("接口返回错误或数据格式不正确:", res);
      tableData.value = [];
      totalRecords.value = 0;
      totalPages.value = 0;
    }
  } catch (error) {
    console.error("获取交通事件数据失败:", error);
    tableData.value = [];
    totalRecords.value = 0;
    totalPages.value = 0;
  } finally {
    loading.value = false;
  }
};

// 事件处理方法
const handleSearch = () => {
  currentPage.value = 1;
  getData();
};

const handleReset = () => {
  selectedEventType.value = "";
  selectedEventState.value = "";
  selectedDepartment.value = "";
  currentPage.value = 1;
  getData();
};

const handlePageChange = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
    getData();
  }
};

// 打开弹窗
const open = () => {
  modalRef.value?.open();
  // 打开弹窗时获取数据
  getData();

  // 临时测试数据（如果接口有问题）
  // setTimeout(() => {
  //   if (tableData.value.length === 0) {
  //     console.log("使用测试数据");
  //     tableData.value = [
  //       {
  //         id: 1,
  //         eventType: 1,
  //         state: 0,
  //         departmentId: "格贡一",
  //         eventTime: "2025-08-04 09:00:59",
  //         updateTime: null,
  //         locationZhuang: "K2807+415"
  //       }
  //     ];
  //     totalRecords.value = 1;
  //     totalPages.value = 1;
  //   }
  // }, 2000);
};

defineExpose({
  open,
});
</script>

<style lang="scss" scoped>
/* 交通事件检测记录弹窗样式 - 与明细台账保持一致 */
.detail-modal-content {
  min-height: 200px;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 筛选区域样式 */
.filter-section {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 24px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  font-size: 14px;
  color: #c4e5ff;
  font-family: Alibaba PuHuiTi;
  white-space: nowrap;
}

/* Arco Design 组件样式覆盖 */
:deep(.arco-select) {
  min-width: 120px;

  .arco-select-view-single {
    background: rgba(0, 0, 0, 0.3) !important;
    border: 1px solid rgba(0, 138, 255, 0.3) !important;
    color: #c4e5ff !important;

    &:hover {
      border-color: #008aff !important;
    }
  }

  .arco-select-view-value {
    color: #c4e5ff !important;
  }

  .arco-select-placeholder {
    color: rgba(196, 229, 255, 0.6) !important;
  }
}

:deep(.arco-select-view-single) {
  background-color: #001120;
}

/* 按钮样式 - 与明细台账保持一致 */
:deep(.arco-btn) {
  width: 40px;
  height: 32px;
  box-sizing: border-box;
  border: 1px solid #10477b;
  border-radius: 4px;
  background-color: #001120;
  font-family: Alibaba PuHuiTi;
  font-size: 12px;
  color: #c4e5ff;

  &.search {
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
      radial-gradient(0% 87% at -13% 112%, #64c6ff 0%, rgba(8, 62, 115, 0) 100%),
      rgba(0, 138, 255, 0.3);
    box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
    border-image: linear-gradient(
        90deg,
        rgba(0, 138, 255, 0),
        rgba(0, 138, 255, 1),
        rgba(0, 138, 255, 0.2),
        rgba(0, 138, 255, 0)
      )
      1 1;
    color: white;
  }

  &.reset {
    &:hover {
      background-color: rgba(16, 71, 123, 0.3);
      border-color: #10477b;
    }
  }
}

/* 表格容器样式 */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* 表格区域样式 */
.table-section {
  flex: 1;
  min-height: 0;
}

/* Arco Design Table 样式覆盖 */
:deep(.arco-table) {
  background: transparent;
  font-family: Alibaba PuHuiTi;

  .arco-table-container {
    border: none !important;
  }

  .arco-table-thead th {
    background: rgba(0, 138, 255, 0.1) !important;
    color: #c4e5ff !important;
    border-bottom: 1px solid rgba(0, 138, 255, 0.2) !important;
    font-weight: bold;
    font-size: 12px;
    padding: 12px 8px;
  }

  .arco-table-tbody td {
    background: transparent !important;
    color: rgba(196, 229, 255, 0.9) !important;
    border-bottom: 1px solid rgba(0, 138, 255, 0.1) !important;
    font-size: 12px;
    padding: 12px 8px;
  }

  .arco-table-tbody .arco-table-tr:hover td {
    background: rgba(0, 138, 255, 0.05) !important;
  }

  .arco-table-empty {
    color: rgba(196, 229, 255, 0.6);
  }

  .arco-table-th {
    font-family: Alibaba PuHuiTi;
    font-weight: normal;
    font-size: 20px;
    color: #ffffff;
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
      rgba(0, 138, 255, 0.3);
    border-image: linear-gradient(
        90deg,
        rgba(0, 138, 255, 0),
        rgba(0, 138, 255, 1),
        rgba(0, 138, 255, 0.2),
        rgba(0, 138, 255, 0)
      )
      1 1;
  }

  .arco-table-td {
    font-family: Alibaba PuHuiTi;
    background-color: transparent;
    border-bottom: 1px solid rgba(0, 138, 255, 0.4);
    font-weight: normal;
    font-size: 16px;
    color: #ffffff;
    text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.25);
    font-style: normal;
    text-transform: none;
  }

  .arco-table-border:not(.arco-table-border-cell) .arco-table-container {
    border: none;
  }

  .arco-table-tr:hover {
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 47%, #008aff 100%),
      rgba(0, 138, 255, 0.05);
    box-shadow: inset 0px 0px 8px 1px rgba(0, 138, 255, 0.25);
    border-radius: 0px 0px 0px 0px;
  }

  .arco-scrollbar-thumb-direction-horizontal .arco-scrollbar-thumb-bar {
    background: linear-gradient(90deg, #00d2ff 0%, rgba(0, 210, 255, 0) 100%), #3168ff;
    box-shadow: 0px 0px 50px 0px #0072ff;
    border-radius: 8px 8px 8px 8px;
  }
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background: rgba(30, 60, 100, 0.05);
  border-top: 1px solid rgba(0, 138, 255, 0.2);
  font-family: Alibaba PuHuiTi;
  flex-shrink: 0;
}

.pagination-info {
  font-size: 14px;
  color: #8a9ba8;

  .pagination-number {
    color: #008aff;
    font-weight: 500;
  }
}

.pagination {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  min-width: 32px;
  height: 32px;
  padding: 0 8px;
  border: 1px solid rgba(0, 138, 255, 0.3);
  background: transparent;
  color: #8a9ba8;
  font-size: 14px;
  font-family: Alibaba PuHuiTi;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled):not(.ellipsis) {
    border-color: #008aff;
    color: #c4e5ff;
    background: rgba(0, 138, 255, 0.1);
  }

  &.active {
    border-color: #008aff;
    background: linear-gradient(180deg, rgba(0, 138, 255, 0) 0%, #008aff 100%);
    color: #ffffff;
    box-shadow: 0px 0px 8px 0px rgba(0, 138, 255, 0.4);
  }

  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
  }

  &.ellipsis {
    cursor: default;
    border: none;
    background: transparent;

    &:hover {
      border: none;
      background: transparent;
      color: #8a9ba8;
    }
  }
}
</style>
