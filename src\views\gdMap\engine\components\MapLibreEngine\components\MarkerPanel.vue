<template>
	<div class="marker-panel" :class="{ 'is-danger': props.alarm }" :style="panelStyle">
		<div class="panel-line-dotted" />
		<div class="panel-line-rhombus" />
		<div class="panel-header">
			<span class="panel-header-title">{{ title }}</span>
			<slot name="header-extra" />
		</div>
		<div class="panel-content">
			<slot name="content" />
		</div>
	</div>
</template>

<script setup>
const props = defineProps({
	title: {
		type: String,
		default: "",
	},
	anchor: {
		type: String,
		default: "default",
	},
	alarm: {
		type: Boolean,
		default: false,
	},
});

const panelStyle = computed(() => {
	return {
		default: {
			right: "68px",
			top: "-15.5px",
		},
		"right-bottom": {
			left: "72%",
			top: "72%",
		},
	}[props.anchor];
});
</script>

<style scoped lang="scss">
@import "./common.scss";
</style>
