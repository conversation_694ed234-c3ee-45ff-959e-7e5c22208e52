<template>
  <TrafficCard title="气象信息">
    <div class="weather-info">
      <div class="card-content">
        <div class="left">
          <a-dropdown trigger="click">
            <a-button class="city">
              <location />
              <div class="city-name">{{ currentCity }}</div>
              <caret-down />
            </a-button>
            <template #content>
              <a-doption
                v-for="{ label, value } in cityList"
                :key="value"
                @click="getData(value)"
                >{{ label }}</a-doption
              >
            </template>
          </a-dropdown>
          <img class="weather-icon" :src="currentWeatherIcon" />
          <div class="weather-desc">
            <div class="weather-desc-text">{{ weatherData.weather }}</div>
            <!-- <div class="weather-desc-split">/</div>
            <div class="weather-desc-temp">{{ weatherData.temp }}</div> -->
            <!-- <div class="weather-desc-unit">°C</div> -->
          </div>
          <div class="temp-info">
            <span class="temp-number">
              {{ weatherData.temp }}
            </span>
            <span class="unit">℃</span>
            <span class="devide"></span>
            <span class="level">{{ weatherData.windLevel }}</span>
            <span class="unit">级</span>
          </div>
          <!-- <div class="warning">
            <component :is="warningIcon" class="warning-icon" />
            <div v-if="weatherData.alarmType" class="warning-text">
              {{ weatherData.alarmType }}预警
            </div>
          </div> -->
        </div>
        <div class="divider"></div>
        <div class="right">
          <div class="r-title">
            <span>未来3天天气预报</span>
            <span>{{ updateTime }} 更新</span>
          </div>
          <div class="r-divide"></div>
          <div v-for="(forecast, index) in dailyForecastData" :key="index" class="r-feature">
            <div class="date">{{ forecast.date }}</div>
            <div class="weather">
              <img :src="forecast.icon" />
              <span>{{ forecast.weather }}</span>
            </div>
            <div class="temp">{{ forecast.temp }}℃</div>
          </div>
          <!-- <div class="right-top">
            <div class="weather-metrics">
              <div v-for="item in 4" class="weather-metrics-item">
                <warning-yellow />
                <div class="weather-metrics-item-text">湿度32°C</div>
              </div>
            </div>
          </div>
          <div class="right-divider"></div>
          <div class="right-footer">
            <div class="weather-hours">
              <div v-for="i in 6" class="weather-hours-item">
                <div class="weather-hours-item-time">11时</div>
                <div class="weather-hours-item-icon">
                  <img src="/assets/images/icons/weather/104.png" />
                </div>
                <div class="weather-hours-item-temp">32°C</div>
                <div class="weather-hours-item-humi">
                  <warning-yellow />
                  0.0
                </div>
              </div>
            </div>
          </div> -->
        </div>
      </div>
    </div>
    <!-- <template #extra>
      <div class="link" @click="onInfo">更多天气 <icon-double-right /></div>
    </template> -->
  </TrafficCard>
</template>

<script setup>
import { ref, computed, unref, onMounted, onBeforeUnmount } from "vue";
import request from "@/utils/request";
import location from "@/assets/modules/traffic/icon/location.svg?component";
import caretDown from "@/assets/modules/traffic/icon/caret-down.svg?component";
import warningBlue from "@/assets/modules/traffic/icon/warning-blue.svg?component";
import warningYellow from "@/assets/modules/traffic/icon/warning-yellow.svg?component";
import warningOrange from "@/assets/modules/traffic/icon/warning-orange.svg?component";
import warningRed from "@/assets/modules/traffic/icon/warning-red.svg?component";

// 导入天气图标
import cloudIcon from "@/assets/images/weather/cloud.png";
import fogIcon from "@/assets/images/weather/fog.png";
import hazeIcon from "@/assets/images/weather/haze.png";
import rainIcon from "@/assets/images/weather/rain.png";
import snowIcon from "@/assets/images/weather/snow.png";
import negativeIcon from "@/assets/images/weather/negative.png";
import sunnyIcon from "@/assets/images/weather/sunny.png";

const cityList = ref([{ label: "格尔木市", value: "格尔木" }]);

const weatherData = ref({});
const currentCity = ref("格尔木市");
const updateTime = ref("");

const warningIcon = computed(() => {
  const { alarmLevel } = unref(weatherData);
  if (alarmLevel) {
    return {
      蓝色: warningBlue,
      黄色: warningYellow,
      橙色: warningOrange,
      红色: warningRed,
    }[alarmLevel];
  }
  return null;
});

// 当前天气图标
const currentWeatherIcon = computed(() => {
  const { weather } = unref(weatherData);
  if (weather) {
    return getWeatherIcon(weather);
  }
  return sunnyIcon; // 默认图标
});

// 更新时间显示
const updateCurrentTime = () => {
  const now = new Date();
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  updateTime.value = `${hours}:${minutes}`;
};

// 定时器变量
let timeUpdateTimer = null;

// 根据天气状况获取对应的图标
const getWeatherIcon = (weather) => {
  if (weather.includes("云")) {
    return cloudIcon;
  } else if (weather.includes("雾")) {
    return fogIcon;
  } else if (weather.includes("霾")) {
    return hazeIcon;
  } else if (weather.includes("雨")) {
    return rainIcon;
  } else if (weather.includes("雪")) {
    return snowIcon;
  } else if (weather.includes("阴")) {
    return negativeIcon;
  } else if (weather.includes("晴")) {
    return sunnyIcon;
  } else {
    // 默认图标
    return sunnyIcon;
  }
};

// 处理未来3天天气预报数据
const dailyForecastData = computed(() => {
  const { dailyForecast } = unref(weatherData);
  if (dailyForecast) {
    try {
      // 将字符串转换为JSON对象
      const forecastArray = JSON.parse(dailyForecast);
      const processedData = forecastArray.map((item) => ({
        date: item.date,
        weather: item.weather,
        temp: Math.round(item.temp), // 四舍五入温度值
        icon: getWeatherIcon(item.weather), // 添加图标路径
      }));
      console.log("处理后的预报数据:", processedData);
      return processedData;
    } catch (error) {
      console.error("解析dailyForecast数据失败:", error);
      return [];
    }
  }
  return [];
});

onMounted(() => {
  getCityList(); // 先获取城市列表，然后获取默认城市天气

  // 初始化时间显示
  updateCurrentTime();

  // 设置定时器，每小时更新一次时间（3600000毫秒 = 1小时）
  timeUpdateTimer = setInterval(updateCurrentTime, 3600000);
});

onBeforeUnmount(() => {
  // 清理定时器
  if (timeUpdateTimer) {
    clearInterval(timeUpdateTimer);
    timeUpdateTimer = null;
  }
});

const getData = (value) => {
  request.get("/api/screen/baotong/weather", { city: value }).then((res) => {
    weatherData.value = res.data;
    console.log("天气数据:", res.data);
    console.log("dailyForecast原始数据:", weatherData.value.dailyForecast);
    // 更新当前显示的城市名称
    const selectedCity = cityList.value.find((city) => city.value === value);
    if (selectedCity) {
      currentCity.value = selectedCity.label;
    }
  });
};

// 获取城市列表接口
const getCityList = () => {
  request
    .get("/api/screen/baotong/weather/city")
    .then((res) => {
      console.log("城市列表数据:", res);
      console.log("城市列表详细数据:", res.data);

      // 将接口返回的城市数据转换为下拉选项格式
      if (res.data && Array.isArray(res.data)) {
        cityList.value = res.data.map((city) => ({
          label: city === "格尔木" ? "格尔木市" : city, // 保持格尔木显示为格尔木市
          value: city,
        }));

        // 获取第一个城市的天气数据
        if (cityList.value.length > 0) {
          getData(cityList.value[0].value);
        }
      }
    })
    .catch((error) => {
      console.error("获取城市列表失败:", error);
      // 如果接口失败，使用默认城市获取天气
      getData("格尔木");
    });
};

const onInfo = () => {};
</script>

<style lang="scss" scoped>
.weather-info {
  height: 138px;
  width: 100%;
}

.card-content {
  display: flex;
  flex-direction: row;
  width: 100%;
  height: 100%;
  overflow: hidden;

  .left {
    width: 102px;
    padding: 12px 8px;
    display: flex;
    flex-direction: column;
    align-items: center;
    .temp-info {
      margin-top: 5px;
      font-family: D-DIN-PRO;
      padding: 3px 8px;
      color: #ffffff;
      background: linear-gradient(180deg, #008aff 0%, #0064b8 100%);
      border-radius: 4px 4px 4px 4px;
      /* opacity: 0.4; */
      span {
        display: inline-block;
      }
      .unit {
        font-size: 12px;
      }
      .devide {
        margin: 0 8px;
        height: 60%;
        width: 1px;
        background-color: #ffffff;
        opacity: 0.6;
      }
    }
  }

  .divider {
    width: 0;
    height: 129px;
    border: 1px solid;
    box-shadow: inset 0px 0px 8px 0px rgba(1, 86, 127, 0.4);
    border-image: linear-gradient(
        90deg,
        rgba(0, 138, 255, 0),
        rgba(0, 138, 255, 0.4),
        rgba(0, 138, 255, 0)
      )
      1 1;
  }

  .right {
    padding: 10px;
    flex: 1;
    color: #ffffff;
    font-family: "Alibaba PuHuiTi";
    font-weight: normal;
    font-size: 14px;
    .r-title {
      display: flex;
      justify-content: space-between;
    }
    .r-divide {
      margin: 10px 0;
      width: 100%;
      height: 0px;
      box-shadow: inset 0px 0px 8px 0px rgba(1, 86, 127, 0.4);
      border-radius: 0px 0px 0px 0px;
      border: 1px solid;
      border-image: linear-gradient(
          90deg,
          rgba(0, 138, 255, 0),
          rgba(131, 198, 255, 1),
          rgba(0, 138, 255, 0)
        )
        1 1;
    }
    .r-feature {
      margin: 8px 0;
      display: flex;
      align-items: center;

      .date {
        width: 40%;
        /* flex: 1; */
        text-align: left;
        font-size: 12px;
        color: #ffffff;
      }

      .weather {
        /* flex: 1; */
        width: 30%;
        overflow-x: visible;
        text-wrap: nowrap;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        font-size: 12px;
        color: #ffffff;

        img {
          width: 20px;
          height: 20px;
          margin-right: 4px;
        }
      }

      .temp {
        flex: 1;
        /* width: 50px; */
        text-align: right;
        font-size: 12px;
        color: #ffffff;
      }
    }
    /* width: 292px; */

    /* &-top {
      height: 58px;
    }

    &-divider {
      width: 100%;
      height: 0px;
      border: 1px solid;
      border-image: linear-gradient(
          90deg,
          rgba(0, 138, 255, 0),
          rgba(0, 138, 255, 0.4),
          rgba(0, 138, 255, 0)
        )
        1 1;
    }

    &-footer {
      height: 78px;
    } */
  }
}

.city {
  height: 22px;
  margin: 0;
  padding: 0 4px;
  border: none;
  background-color: transparent;
  display: flex;
  align-items: center;

  &-name {
    flex: 1;
    font-family: Alibaba PuHuiTi;
    font-size: 16px;
    color: #ffffff;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    margin: 0 4px;
  }
}

.weather-icon {
  width: 36px;
  aspect-ratio: 1;
  margin-top: 6px;
}

.weather-desc {
  display: flex;
  flex-direction: row;
  margin-top: 8px;
  font-size: 14px;
  color: #ffffff;

  &-text {
    font-family: Alibaba PuHuiTi;
    font-weight: 500;
  }

  &-split {
    font-family: Alibaba PuHuiTi;
    font-weight: 500;
    padding: 0 4px;
  }

  &-temp {
    font-family: D-DIN-PRO;
    font-weight: 500;
  }

  &-unit {
    font-family: D-DIN-PRO;
    font-weight: 700;
  }
}

.warning {
  margin-top: 4px;
  display: flex;
  flex-direction: row;

  &-icon {
    width: 18px;
    aspect-ratio: 1;
  }

  &-text {
    margin-left: 4px;
    font-family: Alibaba PuHuiTi;
    font-size: 12px;
    color: #ffad29;
    display: flex;
    align-items: center;
  }
}

.weather-metrics {
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;

  &-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    svg {
      width: 24px;
      height: 24px;
    }

    &-text {
      font-family: Alibaba PuHuiTi, D-DIN-PRO;
      font-size: 12px;
      color: #008aff;
    }
  }
}

.weather-hours {
  height: 100%;
  display: flex;
  flex-direction: row;
  justify-content: space-evenly;
  &-item {
    display: flex;
    flex-direction: column;
    row-gap: 4px;
    align-items: center;
    justify-content: center;
    font-family: Alibaba PuHuiTi, D-DIN-PRO;
    font-size: 12px;
    color: #ffffff;

    // &-time {
    // }

    &-icon {
      img {
        width: 16px;
        height: 16px;
      }
    }

    // &-temp {
    // }

    &-humi {
      display: flex;
      align-items: center;

      svg {
        width: 12px;
        height: 12px;
      }
    }
  }
}

.link {
  display: flex;
  flex-direction: row;
  align-items: center;
  cursor: pointer;
}
</style>
