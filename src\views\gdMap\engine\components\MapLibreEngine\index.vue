<template>
	<div class="maplibre-engine" ref="mapContainer">
		<!-- 调试信息框 -->
		<div v-if="showDebugInfo" class="debug-info">
			<div>
				<div>缩放层级:</div>
				<div>
					{{ debugInfo.zoom }}
				</div>
				<div>
					{{ debugInfo.center.lng.toFixed(6) }},
					{{ debugInfo.center.lat.toFixed(6) }}
				</div>
			</div>

			<div>
				<div>鼠标坐标:</div>
				<div>
					{{ debugInfo.mouse.lng.toFixed(6) }},
					{{ debugInfo.mouse.lat.toFixed(6) }}
				</div>
			</div>
		</div>
		<div class="mask"></div>
	</div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount } from "vue";
import maplibregl from "maplibre-gl";
import "maplibre-gl/dist/maplibre-gl.css";
import mapConfig from "@/config/engine/maplibre/map.config.js";
import locationsConfig from "./config/locations.config.js";
import { LayerManager } from "./layers/LayerManager";
import emitter from "@/utils/emitter";

// 调试信息相关变量
const showDebugInfo = ref(false); // 控制调试框显示/隐藏
const debugInfo = ref({
	zoom: 0,
	center: { lng: 0, lat: 0 },
	mouse: { lng: 0, lat: 0 },
});

const mapContainer = ref(null);
let map = null;

// 添加图层管理器
let layerManager = null;

const engineStatus = ref({
	initialized: false,
	paused: false,
});

// 初始化地图
const init = async (options = {}) => {
	if (engineStatus.value.initialized) return map;

	try {
		// 使用配置文件中的参数
		const mapOptions = {
			container: mapContainer.value,
			center: mapConfig.center,
			zoom: mapConfig.minzoom,
			minZoom: mapConfig.minzoom,
			maxZoom: mapConfig.maxzoom,

			// style: 'https://demotiles.maplibre.org/style.json',
			style: {
				version: 8,
				// 添加字体定义
				glyphs: "https://demotiles.maplibre.org/font/{fontstack}/{range}.pbf",
				sources: {
					// "base-layer": {
					// 	type: "raster",
					// 	tiles: [
					// 		`${mapConfig.baseLayerUrl}?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=${mapConfig.layerName}&STYLE=&TILEMATRIXSET=${mapConfig.tileMatrixSet}&FORMAT=image/png&TILEMATRIX=${mapConfig.tileMatrixSet}:{z}&TILEROW={y}&TILECOL={x}`,
					// 	],
					// 	tileSize: 256,
					// },
					// 天地图矢量底图 - 使用直接访问并随机负载均衡
					// "tianditu-vec": {
					// 	type: "raster",
					// 	tiles: [
					// 		`/tianditu/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=7eb2601ad8b7be9b959e476d44e5b3a8`,
					// 	],
					// 	tileSize: 256,
					// },
					// // 天地图矢量注记 - 使用直接访问并随机负载均衡
					// "tianditu-cva": {
					// 	type: "raster",
					// 	tiles: [
					// 		`/tianditu/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={z}&TILEROW={y}&TILECOL={x}&tk=7eb2601ad8b7be9b959e476d44e5b3a8`,
					// 	],
					// 	tileSize: 256,
					// },
				},
				layers: [
					// {
					//   id: 'base-layer',
					//   type: 'raster',
					//   source: 'base-layer',
					//   // minzoom: mapConfig.minzoom,
					//   maxzoom: mapConfig.maxzoom + 1,
					// },
					// 天地图矢量底图图层
					// {
					// 	id: "tianditu-vec-layer",
					// 	type: "raster",
					// 	source: "tianditu-vec",
					// 	minzoom: 1,
					// 	maxzoom: 18,
					// },
					// 天地图矢量注记图层
					// {
					// 	id: "tianditu-cva-layer",
					// 	type: "raster",
					// 	source: "tianditu-cva",
					// 	minzoom: 1,
					// 	maxzoom: 18,
					// },
				],
			},

			maxBounds: mapConfig.bounds,
			attributionControl: false, // 关闭版权和属性信息
			doubleClickZoom: false, // 双击事件关闭
			...options,
		};

		// 创建地图实例
		map = new maplibregl.Map(mapOptions);

		// 添加自定义处理，确保底图在任何缩放级别都可见
		map.on("zoom", () => {
			const currentZoom = map.getZoom();
			// console.log(currentZoom);
			// 更新调试信息
			debugInfo.value.zoom = currentZoom.toFixed(2);
			debugInfo.value.center = map.getCenter();
		});

		// 添加鼠标移动事件，更新鼠标坐标
		map.on("mousemove", (e) => {
			debugInfo.value.mouse = e.lngLat;
		});

		// 添加地图移动事件，更新中心点坐标
		map.on("move", () => {
			debugInfo.value.center = map.getCenter();
		});

		// 等待地图加载完成
		await new Promise((resolve) => {
			// 在地图加载完成后
			map.on("load", async () => {
				try {
					// 初始化图层管理器
					layerManager = new LayerManager(map);
					// 初始化所有图层
					await layerManager.initLayers();
					// 初始化调试信息
					debugInfo.value.zoom = map.getZoom().toFixed(2);
					debugInfo.value.center = map.getCenter();
					engineStatus.value.initialized = true;

					emitter.$emit("engineOnLoad", {
						type: "maplibre",
						instance: map,
						layerManager,
						status: engineStatus.value,
					});

					resolve(map);
				} catch (error) {
					console.error("MapLibre 引擎初始化失败:", error);
				}
			});
		});

		return map;
	} catch (error) {
		console.error("MapLibre 引擎初始化失败:", error);

		throw error;
	}
};

// 切换调试信息显示状态
const toggleDebugInfo = () => {
	showDebugInfo.value = !showDebugInfo.value;
};

// 暂停引擎
const pause = () => {
	if (!map || engineStatus.value.paused) return;

	// 停止渲染循环，减少资源占用
	map.stop();
	engineStatus.value.paused = true;
};

// 恢复引擎
const resume = () => {
	if (!map || !engineStatus.value.paused) return;

	engineStatus.value.paused = false;
};

// 降低性能以节省资源
const lowerPerformance = () => {
	if (!map) return;

	// 可以降低地图的帧率或禁用某些图层
	pause();
};

// 定位到指定位置
const locateToPosition = (options) => {
	if (!map) return;

	const { center, zoom } = options;

	if (!center || !zoom) {
		console.warn("定位数据不完整", options);
		return;
	}

	// console.log(`定位到${options}`);

	map.flyTo({
		center,
		zoom,
		duration: 3000,
		bearing: 0,
		pitch: 0,
		essential: true,
	});
};

// 定位到人员预警标记
const locateToPersonnelAlert = ({ id, coordinates, zoom = 12 }) => {
	if (!id || !coordinates) return;

	console.log(`定位到人员预警标记: ${id}`, coordinates);

	map.flyTo({
		center: coordinates,
		zoom: zoom,
		duration: 3000,
		essential: true,
	});
};

// 重置地图视角到默认位置（总览）
const resetMapView = () => {
	if (!map) return;

	// // 使用locations.config.js中的"all"位置参数
	// const defaultViewConfig = locationsConfig.locations.all;

	// console.log("重置地图视角到默认位置", defaultViewConfig);

	// map.flyTo({
	// 	...locationsConfig.defaultFlyOptions,
	// 	...defaultViewConfig,
	// 	essential: true,
	// });

	map.flyTo({
		center: mapConfig.center,
		zoom: mapConfig.minzoom,
		duration: 3000,
		essential: true,
	});
};

// 销毁引擎
const destroy = () => {
	if (map) {
		map.remove();
		map = null;
		engineStatus.value.initialized = false;
		engineStatus.value.paused = false;

		// 销毁所有图层
		if (layerManager) {
			layerManager.destroy();
			layerManager = null;
		}
	}
};

// 组件挂载时自动初始化
onMounted(async () => {
	// 注意：这里不自动初始化，而是等待父组件调用init方法

	emitter.$on("pageNavChange", (event) => {
		// 从配置文件获取位置信息
		const locationConfig = locationsConfig.locations[event];

		if (locationConfig) {
			// 使用配置的位置信息进行飞行
			map.flyTo({
				...locationsConfig.defaultFlyOptions, // 应用默认选项
				...locationConfig, // 应用特定位置的选项（会覆盖默认选项）
			});

			// 可以添加额外的场景特定逻辑
			if (event === "gegong3") {
				// 例如：高亮特定道路
				// roadLayer.highlightRoad('gegong3-road');
			}
		} else {
			console.warn(`未找到位置配置: ${event}`);
		}
	});

	// 添加统一的定位事件监听
	emitter.$on("locateToPosition", locateToPosition);
});

// 组件卸载前清理资源
onBeforeUnmount(() => {
	destroy();
});

// 对外暴露方法
defineExpose({
	init,
	pause,
	resume,
	lowerPerformance,
	destroy,
	getMap: () => map,
	engineStatus,
	toggleDebugInfo, // 暴露调试信息切换方法
	showDebugInfo, // 暴露调试信息显示状态
	locateToPosition, // 暴露统一的定位方法
	locateToPersonnelAlert, // 暴露人员预警定位方法
	resetMapView, // 暴露重置视角方法
});
</script>

<style scoped>
.maplibre-engine {
	width: 100%;
	height: 100%;
	position: relative;
}

.debug-info {
	position: absolute;
	bottom: 300px;
	right: 50%;
	transform: translateX(-50%);
	background-color: rgba(0, 0, 0, 0.7);
	color: white;
	padding: 10px;
	border-radius: 4px;
	font-size: 14px;
	z-index: 1000;
	pointer-events: none; /* 允许点击穿透 */
	min-width: 250px;
	text-align: left;
	div {
		margin-bottom: 10px;
	}
}

.mask {
	z-index: 1;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	clip-path: inset(0% 0% 0% 0%);
	/* background: radial-gradient(
		circle at center,
		rgba(0, 0, 0, 0) 0%,
		rgba(0, 0, 0, 0.2) 50%,
		rgba(0, 0, 0, 0.7) 96%,
		rgba(0, 0, 0, 0.8) 98%,
		rgba(0, 0, 0, 1) 100%
	); */

	background: linear-gradient(
			to bottom,
			rgba(0, 0, 0, 1),
			rgba(0, 0, 0, 0) 20%,
			rgba(0, 0, 0, 0) 80%,
			rgba(0, 0, 0, 1)
		),
		linear-gradient(
			to right,
			rgba(0, 0, 0, 1),
			rgba(0, 0, 0, 0) 20%,
			rgba(0, 0, 0, 0) 80%,
			rgba(0, 0, 0, 1)
		);
	pointer-events: none;
}
</style>
